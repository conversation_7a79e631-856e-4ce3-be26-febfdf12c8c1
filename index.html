<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Excellence – Specialists in High End Audio, Home Theatre & Home Automation</title>
    <meta name="description" content="Audio Excellence: Specialists in High End Audio, Home Theatre & Home Automation. 30+ years of passion and expertise you can't get from an online store.">
    <meta name="keywords" content="High End Audio, Home Theatre, Home Automation, Luxury Audio, Audiophile, Cinema, Smart Home, Audio Excellence">
    <meta property="og:title" content="Audio Excellence – High End Audio, Home Theatre & Home Automation">
    <meta property="og:description" content="Experience luxury audio, home theatre, and automation with Audio Excellence. Visit our showroom.">
    <meta property="og:image" content="/images/Header-Home-Parallax.jpg">
    <meta property="og:type" content="website">
    <link rel="icon" type="image/png" href="/images/logo.png">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="/assets/css/brands.css">
    <link rel="stylesheet" href="/assets/css/solid.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.4/dist/aos.css">
    <style>
        :root {
            --lux-black: #000000;
            --lux-white: #ffffff;
            --lux-orange: #FF4A00;
        }
        body {
            background: var(--lux-black);
            color: var(--lux-white);
            font-family: 'Open Sans', 'Source Sans 3', Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        .lux-hero {
            position: relative;
            min-height: 100vh;
            background: url('/images/Header-Home-Parallax.jpg') center/cover no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .lux-hero::before {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(0,0,0,0.7);
            z-index: 1;
        }
        .lux-hero-content {
            position: relative;
            z-index: 2;
            color: var(--lux-white);
            max-width: 800px;
            margin: 0 auto;
        }
        .lux-hero h1 {
            font-size: 2.8rem;
            font-weight: 700;
            letter-spacing: 0.03em;
            text-shadow: 0 4px 32px #000, 0 0 8px var(--lux-orange);
        }
        .lux-hero .lux-sub {
            font-size: 1.3rem;
            font-weight: 400;
            margin: 1.5rem 0 2.5rem 0;
            color: #fff;
            opacity: 0.85;
        }
        .lux-hero .lux-soundwave {
            margin: 2rem auto 0 auto;
            width: 100%;
            max-width: 500px;
            height: 60px;
        }
        .lux-cta-sticky {
            position: fixed;
            right: 2vw;
            bottom: 3vh;
            z-index: 1000;
            background: var(--lux-orange);
            color: #fff;
            padding: 1rem 2.2rem;
            border-radius: 2rem;
            font-weight: 700;
            font-size: 1.1rem;
            box-shadow: 0 4px 32px #0008;
            transition: background 0.2s;
            text-decoration: none;
        }
        .lux-cta-sticky:hover {
            background: #fff;
            color: var(--lux-orange);
        }
        .lux-section {
            padding: 5rem 0 3rem 0;
            background: #111;
        }
        .lux-section.alt {
            background: #181818;
        }
        .lux-section-title {
            font-size: 2.2rem;
            font-weight: 600;
            color: var(--lux-orange);
            margin-bottom: 2rem;
            text-align: center;
        }
        .lux-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            justify-content: center;
        }
        .lux-card {
            background: #181818;
            border-radius: 1.2rem;
            box-shadow: 0 2px 16px #000a;
            padding: 2.2rem 2rem 2rem 2rem;
            min-width: 260px;
            max-width: 340px;
            flex: 1 1 260px;
            color: #fff;
            transition: transform 0.2s, box-shadow 0.2s;
            position: relative;
            overflow: hidden;
        }
        .lux-card:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 8px 32px var(--lux-orange), 0 2px 16px #000a;
        }
        .lux-card .lux-icon {
            font-size: 2.5rem;
            color: var(--lux-orange);
            margin-bottom: 1.2rem;
        }
        .lux-card-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.7rem;
        }
        .lux-card-desc {
            font-size: 1rem;
            opacity: 0.85;
        }
        .lux-brand-carousel {
            background: #000;
            padding: 2.5rem 0;
            overflow-x: auto;
        }
        .lux-brands {
            display: flex;
            gap: 2.5rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        .lux-brand {
            filter: grayscale(1) brightness(1.2);
            transition: filter 0.3s;
            height: 60px;
            display: flex;
            align-items: center;
        }
        .lux-brand:hover {
            filter: none;
        }
        .lux-3col {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        .lux-3col-item {
            background: #181818;
            border-radius: 1rem;
            padding: 2rem 1.5rem;
            min-width: 220px;
            max-width: 320px;
            flex: 1 1 220px;
            text-align: center;
        }
        .lux-3col-item .lux-icon {
            font-size: 2.2rem;
            color: var(--lux-orange);
            margin-bottom: 1rem;
        }
        .lux-showroom-invite {
            background: #111;
            color: #fff;
            text-align: center;
            padding: 3rem 1rem 2rem 1rem;
            font-size: 1.3rem;
            font-weight: 500;
            border-radius: 1.5rem;
            margin: 3rem auto 0 auto;
            max-width: 700px;
            box-shadow: 0 2px 16px #000a;
        }
        .lux-footer {
            background: #000;
            color: #fff;
            padding: 2.5rem 0 1.2rem 0;
            text-align: center;
            font-size: 1rem;
        }
        .lux-footer .lux-footer-social {
            margin: 1.2rem 0;
        }
        .lux-footer .lux-footer-social a {
            color: var(--lux-orange);
            margin: 0 0.7rem;
            font-size: 1.5rem;
            transition: color 0.2s;
        }
        .lux-footer .lux-footer-social a:hover {
            color: #fff;
        }
        .lux-footer .lux-footer-tagline {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .lux-footer .lux-footer-contact {
            margin-bottom: 0.5rem;
        }
        /* Custom cursor */
        body, a, button, .lux-card {
            cursor: url('/images/logo4.svg'), auto;
        }
        @media (max-width: 900px) {
            .lux-hero h1 { font-size: 2rem; }
            .lux-section { padding: 2.5rem 0 1.5rem 0; }
            .lux-3col { flex-direction: column; gap: 1rem; }
            .lux-cards { flex-direction: column; gap: 1.2rem; }
        }
    </style>
</head>

<body>
    <!-- Replace the existing navbar with a luxury, glassmorphic, cinematic navbar -->
    <nav class="lux-navbar navbar navbar-expand-lg fixed-top" style="backdrop-filter: blur(12px); background: rgba(0,0,0,0.85); box-shadow: 0 8px 32px #000a; border-radius: 0 0 2rem 2rem; margin: 1.2rem 2vw 0 2vw; left: 0; right: 0; width: auto;">
        <div class="container-fluid px-4 py-2" style="min-height: 72px;">
            <a class="navbar-brand d-flex align-items-center lux-logo" href="/" style="gap: 1.2rem;">
                <img src="/images/logo.png" alt="Audio Excellence Logo" style="height: 60px; width: auto; filter: drop-shadow(0 0 12px #FF4A00aa);">
                <span style="font-weight: 800; font-size: 1.6rem; letter-spacing: 0.06em; color: var(--lux-orange); text-shadow: 0 2px 16px #000, 0 0 8px var(--lux-orange);">Audio Excellence</span>
            </a>
            <button class="navbar-toggler lux-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation" style="border: none; outline: none;">
                <span class="navbar-toggler-icon" style="filter: drop-shadow(0 0 6px #FF4A00cc);"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0 lux-nav-list" style="gap: 1.5rem; align-items: center;">
                    <li class="nav-item">
                        <a class="nav-link lux-nav-link active" href="/" style="font-size: 1.1rem; font-weight: 600; padding: 0.7rem 1.2rem; border-radius: 1.5rem; transition: all 0.2s;">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link lux-nav-link" href="/pages/music-systems.html" style="font-size: 1.1rem; font-weight: 600; padding: 0.7rem 1.2rem; border-radius: 1.5rem; transition: all 0.2s;">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link lux-nav-link" href="/pages/home-theatre.html" style="font-size: 1.1rem; font-weight: 600; padding: 0.7rem 1.2rem; border-radius: 1.5rem; transition: all 0.2s;">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link lux-nav-link" href="/pages/home-automation.html" style="font-size: 1.1rem; font-weight: 600; padding: 0.7rem 1.2rem; border-radius: 1.5rem; transition: all 0.2s;">Home Automation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link lux-nav-link" href="/pages/contact.html" style="font-size: 1.1rem; font-weight: 600; padding: 0.7rem 1.2rem; border-radius: 1.5rem; transition: all 0.2s;">Contact</a>
                    </li>
                </ul>
                <a href="#book-demo" class="lux-cta-btn ms-4" style="background: var(--lux-orange); color: #fff; font-weight: 700; font-size: 1.1rem; border-radius: 2rem; padding: 0.7rem 2.2rem; box-shadow: 0 0 16px #FF4A0088, 0 2px 16px #000a; margin-left: 2rem; text-decoration: none; transition: background 0.2s, color 0.2s, box-shadow 0.2s; border: none; outline: none; display: inline-block; position: relative; overflow: hidden;">
                    <span style="position: relative; z-index: 2;">Book a Demo</span>
                    <span class="lux-cta-glow" style="position: absolute; left: 0; top: 0; width: 100%; height: 100%; box-shadow: 0 0 32px 8px #FF4A0055; opacity: 0.7; pointer-events: none;"></span>
                </a>
            </div>
        </div>
    </nav>
    <style>
        .lux-navbar {
            animation: lux-navbar-fadein 1.2s cubic-bezier(.4,1.6,.6,1) 0.2s both;
        }
        @keyframes lux-navbar-fadein {
            0% { opacity: 0; transform: translateY(-40px) scale(0.98); }
            100% { opacity: 1; transform: none; }
        }
        .lux-nav-link {
            color: #fff !important;
            border: 2px solid transparent;
            box-shadow: 0 0 0 #FF4A0000;
        }
        .lux-nav-link.active, .lux-nav-link:hover, .lux-nav-link:focus {
            color: var(--lux-orange) !important;
            background: rgba(255,74,0,0.08);
            border: 2px solid var(--lux-orange);
            box-shadow: 0 0 12px #FF4A0055;
            text-shadow: 0 2px 8px #000, 0 0 4px var(--lux-orange);
        }
        .lux-cta-btn:hover, .lux-cta-btn:focus {
            background: #fff !important;
            color: var(--lux-orange) !important;
            box-shadow: 0 0 32px #FF4A00cc, 0 2px 16px #000a;
        }
        .lux-logo img {
            transition: filter 0.3s, transform 0.3s;
        }
        .lux-logo:hover img {
            filter: drop-shadow(0 0 24px #FF4A00cc) brightness(1.2);
            transform: scale(1.06) rotate(-2deg);
        }
        @media (max-width: 900px) {
            .lux-navbar { margin: 0; border-radius: 0; }
            .lux-logo span { font-size: 1.1rem; }
            .lux-logo img { height: 40px; }
            .lux-cta-btn { padding: 0.6rem 1.2rem; font-size: 1rem; }
        }
    </style>

    <!-- Hero Section -->
    <section class="lux-hero" id="hero">
        <div class="lux-hero-content" data-aos="fade-up">
            <h1>Specialists in High End Audio, Home Theatre & Home Automation</h1>
            <div class="lux-sub">30+ years of passion and expertise you can't get from an online store</div>
            <!-- Soundwave Visualizer Placeholder -->
            <div class="lux-soundwave" id="soundwave-hero"></div>
        </div>
    </section>

    <!-- Sticky CTA -->
    <a href="#book-demo" class="lux-cta-sticky">Book a Demo</a>

    <!-- High-End Audio, Home Theatre, Home Automation, Office & Hospitality -->
    <section class="lux-section" id="solutions">
        <div class="container">
            <div class="lux-section-title" data-aos="fade-up">Our Expertise</div>
            <div class="lux-cards">
                <div class="lux-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="lux-icon"><i class="fa-solid fa-music"></i></div>
                    <div class="lux-card-title">High-End Audio</div>
                    <div class="lux-card-desc">With our specially selected music playback systems, you can relive the music of your favourite bands and artists.</div>
                </div>
                <div class="lux-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="lux-icon"><i class="fa-solid fa-film"></i></div>
                    <div class="lux-card-title">Home Theatre</div>
                    <div class="lux-card-desc">Bring the thrill of the Silver Screen into your home with your own private cinema.</div>
                </div>
                <div class="lux-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="lux-icon"><i class="fa-solid fa-house-signal"></i></div>
                    <div class="lux-card-title">Home Automation</div>
                    <div class="lux-card-desc">Add comfort, convenience, security, and energy efficiency to your lifestyle.</div>
                </div>
                <div class="lux-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="lux-icon"><i class="fa-solid fa-briefcase"></i></div>
                    <div class="lux-card-title">Office & Hospitality Solutions</div>
                    <div class="lux-card-desc">AV and control systems for boardrooms, office spaces, and restaurants.</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Customised Solutions -->
    <section class="lux-section alt" id="customised">
        <div class="container">
            <div class="lux-section-title" data-aos="fade-up">Customised Solutions</div>
            <div class="lux-cards">
                <div class="lux-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="lux-icon"><i class="fa-solid fa-cogs"></i></div>
                    <div class="lux-card-title">Tailored Design</div>
                    <div class="lux-card-desc">Modern card layout with icons and hover animations. Every system is designed for your space and needs.</div>
                </div>
                <div class="lux-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="lux-icon"><i class="fa-solid fa-shield-alt"></i></div>
                    <div class="lux-card-title">Security & Reliability</div>
                    <div class="lux-card-desc">We use only the most reliable, high-quality products and installation practices.</div>
                </div>
                <div class="lux-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="lux-icon"><i class="fa-solid fa-lightbulb"></i></div>
                    <div class="lux-card-title">Smart Integration</div>
                    <div class="lux-card-desc">Seamless integration of lighting, AV, security, blinds, and more.</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Highest Quality Products -->
    <section class="lux-section" id="quality">
        <div class="container">
            <div class="lux-section-title" data-aos="fade-up">Highest Quality Products</div>
            <div class="lux-3col">
                <div class="lux-3col-item" data-aos="fade-up" data-aos-delay="100">
                    <div class="lux-icon"><i class="fa-solid fa-award"></i></div>
                    <div class="lux-card-title">Award-Winning Brands</div>
                    <div class="lux-card-desc">We partner with the world's most respected audio and automation brands.</div>
                </div>
                <div class="lux-3col-item" data-aos="fade-up" data-aos-delay="200">
                    <div class="lux-icon"><i class="fa-solid fa-badge-check"></i></div>
                    <div class="lux-card-title">Certified Installers</div>
                    <div class="lux-card-desc">Our team is factory-trained and certified for every product we install.</div>
                </div>
                <div class="lux-3col-item" data-aos="fade-up" data-aos-delay="300">
                    <div class="lux-icon"><i class="fa-solid fa-star"></i></div>
                    <div class="lux-card-title">Unmatched Support</div>
                    <div class="lux-card-desc">We provide ongoing support and service for every system we deliver.</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Showroom Invitation -->
    <section class="lux-section alt" id="showroom">
        <div class="container">
            <div class="lux-showroom-invite" data-aos="zoom-in">
                This is how music & movies should be experienced… <br> <strong>Visit us and start the experience!</strong>
            </div>
        </div>
    </section>

    <!-- Brand Carousel -->
    <section class="lux-brand-carousel" id="brands">
        <div class="lux-section-title" data-aos="fade-up">Our Brands</div>
        <div class="lux-brands" data-aos="fade-up" data-aos-delay="100">
            <img src="/brands/logo-brand-bluesound.jpg" alt="Bluesound" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-clearaudio-1.jpg" alt="Clearaudio" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-conrad-johnson-1.jpg" alt="Conrad-Johnson" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-control4.jpg" alt="Control4" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-crestron.jpg" alt="Crestron" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-epson.jpg" alt="Epson" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-golden-ear-tech-1.jpg" alt="GoldenEar" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-isotek.jpg" alt="IsoTek" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-kef.jpg" alt="KEF" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-klipsch.jpg" alt="Klipsch" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-magico.jpg" alt="Magico" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-mcintosh.jpg" alt="McIntosh" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-mission.jpg" alt="Mission" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-nad.jpg" alt="NAD" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-primare.jpg" alt="Primare" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-pro-ject.jpg" alt="Pro-Ject" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-rotel.jpg" alt="ROTEL" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-supra-cables.jpg" alt="SUPRA" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-svs-sound-revolution.jpg" alt="SVS" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-wharfedale.jpg" alt="Wharfedale" class="lux-brand" loading="lazy">
            <img src="/brands/logo-brand-yamaha.jpg" alt="Yamaha" class="lux-brand" loading="lazy">
        </div>
    </section>

    <!-- Footer -->
    <footer class="lux-footer">
        <div class="lux-footer-tagline">Audio Excellence – High End Audio</div>
        <div class="lux-footer-contact">
            Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
            (************* &nbsp;|&nbsp; <a href="mailto:<EMAIL>" style="color:var(--lux-orange);text-decoration:none;"><EMAIL></a>
        </div>
        <div class="lux-footer-social">
            <a href="https://facebook.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Facebook"><i class="fa-brands fa-facebook-f"></i></a>
            <a href="https://instagram.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Instagram"><i class="fa-brands fa-instagram"></i></a>
        </div>
        <div style="opacity:0.7;">© 2019 Audio Excellence Pty Ltd.</div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/fontawesome.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <script>
        AOS.init({ once: true, duration: 900 });
        // Placeholder for soundwave visualizer
        document.getElementById('soundwave-hero').innerHTML = '<svg width="100%" height="60" viewBox="0 0 500 60"><polyline points="0,30 20,10 40,50 60,20 80,40 100,10 120,50 140,30 160,10 180,50 200,30 220,10 240,50 260,20 280,40 300,10 320,50 340,30 360,10 380,50 400,30 420,10 440,50 460,20 480,40 500,30" fill="none" stroke="#FF4A00" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" opacity="0.8"><animate attributeName="points" dur="2s" repeatCount="indefinite" values="0,30 20,10 40,50 60,20 80,40 100,10 120,50 140,30 160,10 180,50 200,30 220,10 240,50 260,20 280,40 300,10 320,50 340,30 360,10 380,50 400,30 420,10 440,50 460,20 480,40 500,30;0,40 20,20 40,40 60,30 80,50 100,20 120,40 140,40 160,20 180,40 200,40 220,20 240,40 260,30 280,50 300,20 320,40 340,40 360,20 380,40 400,40 420,20 440,40 460,30 480,50 500,40;0,30 20,10 40,50 60,20 80,40 100,10 120,50 140,30 160,10 180,50 200,30 220,10 240,50 260,20 280,40 300,10 320,50 340,30 360,10 380,50 400,30 420,10 440,50 460,20 480,40 500,30"/></polyline></svg>';
    </script>
</body>

</html>