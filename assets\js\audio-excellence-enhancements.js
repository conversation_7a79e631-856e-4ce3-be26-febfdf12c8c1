/**
 * Audio Excellence - Luxury UX Enhancements
 * Cinematic interactions and premium user experience
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all enhancements
    initSmoothScrolling();
    initParallaxEffects();
    initAudioVisualizer();
    initLuxuryCursor();
    initBrandHoverEffects();
    initCardEnhancements();
    initLoadingAnimation();
    
    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    // Parallax effects for hero sections
    function initParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.lux-parallax, .lux-hero');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            parallaxElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        });
    }
    
    // Audio visualizer animation
    function initAudioVisualizer() {
        const visualizers = document.querySelectorAll('.lux-audio-bars');
        
        visualizers.forEach(visualizer => {
            for (let i = 0; i < 5; i++) {
                const bar = document.createElement('div');
                bar.className = 'lux-audio-bar';
                bar.style.animationDelay = `${i * 0.1}s`;
                visualizer.appendChild(bar);
            }
        });
    }
    
    // Luxury cursor effects
    function initLuxuryCursor() {
        const cursor = document.createElement('div');
        cursor.className = 'lux-custom-cursor';
        cursor.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #FF4A00, #CC3A00);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            mix-blend-mode: difference;
            transition: transform 0.1s ease;
            display: none;
        `;
        document.body.appendChild(cursor);
        
        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX - 10 + 'px';
            cursor.style.top = e.clientY - 10 + 'px';
            cursor.style.display = 'block';
        });
        
        document.addEventListener('mouseenter', () => {
            cursor.style.display = 'block';
        });
        
        document.addEventListener('mouseleave', () => {
            cursor.style.display = 'none';
        });
        
        // Scale cursor on hover over interactive elements
        const interactiveElements = document.querySelectorAll('a, button, .lux-card, .lux-brand');
        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                cursor.style.transform = 'scale(1.5)';
            });
            element.addEventListener('mouseleave', () => {
                cursor.style.transform = 'scale(1)';
            });
        });
    }
    
    // Enhanced brand hover effects
    function initBrandHoverEffects() {
        const brandLogos = document.querySelectorAll('.lux-brand');
        
        brandLogos.forEach(logo => {
            logo.addEventListener('mouseenter', function() {
                this.style.filter = 'none';
                this.style.transform = 'scale(1.1)';
                this.style.boxShadow = '0 5px 20px rgba(255, 74, 0, 0.3)';
            });
            
            logo.addEventListener('mouseleave', function() {
                this.style.filter = 'grayscale(1) brightness(1.2)';
                this.style.transform = 'scale(1)';
                this.style.boxShadow = 'none';
            });
        });
    }
    
    // Card enhancement effects
    function initCardEnhancements() {
        const cards = document.querySelectorAll('.lux-card');
        
        cards.forEach(card => {
            card.classList.add('lux-card-enhanced');
            
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.03)';
                this.style.boxShadow = '0 8px 32px rgba(255, 74, 0, 0.3), 0 2px 16px rgba(0,0,0,0.6)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 2px 16px rgba(0,0,0,0.6)';
            });
        });
    }
    
    // Loading animation
    function initLoadingAnimation() {
        const loader = document.createElement('div');
        loader.className = 'lux-loading';
        loader.innerHTML = `
            <div class="lux-audio-bars" style="height: 60px;">
                <div class="lux-audio-bar"></div>
                <div class="lux-audio-bar"></div>
                <div class="lux-audio-bar"></div>
                <div class="lux-audio-bar"></div>
                <div class="lux-audio-bar"></div>
            </div>
        `;
        document.body.appendChild(loader);
        
        // Hide loader after page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                loader.classList.add('fade-out');
                setTimeout(() => {
                    loader.remove();
                }, 500);
            }, 1000);
        });
    }
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for scroll animations
    document.querySelectorAll('.lux-card, .lux-section-title, .lux-3col-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
    
    // Add floating animation to specific elements
    document.querySelectorAll('.lux-hero img, .lux-brand').forEach(el => {
        el.classList.add('lux-float');
    });
    
    // Enhanced button effects
    document.querySelectorAll('.btn').forEach(btn => {
        if (!btn.classList.contains('lux-btn-enhanced')) {
            btn.classList.add('lux-btn-enhanced');
        }
    });
    
    // Audio context for sound effects (optional)
    let audioContext;
    
    function initAudioContext() {
        if (!audioContext) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
    }
    
    // Add subtle sound effects on hover (optional)
    function playHoverSound() {
        if (!audioContext) return;
        
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }
    
    // Add hover sound to interactive elements (optional - uncomment to enable)
    /*
    document.querySelectorAll('.lux-card, .lux-btn-enhanced').forEach(element => {
        element.addEventListener('mouseenter', () => {
            initAudioContext();
            playHoverSound();
        });
    });
    */
    
});

// Utility function for debouncing scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export for use in other scripts
window.AudioExcellenceEnhancements = {
    initSmoothScrolling,
    initParallaxEffects,
    initAudioVisualizer,
    initLuxuryCursor,
    initBrandHoverEffects,
    initCardEnhancements,
    initLoadingAnimation
};
