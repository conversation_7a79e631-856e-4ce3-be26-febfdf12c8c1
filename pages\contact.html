<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact – Audio Excellence</title>
    <meta name="description" content="Contact Audio Excellence for high-end audio, home theatre, and automation solutions. Drop us a line or visit our showroom.">
    <link rel="icon" type="image/png" href="../image/logo.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700;800;900&family=Helvetica+Neue:wght@300;400;500;600;700&family=Avenir:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Luxury Color Palette */
            --primary-black: #000000;
            --primary-white: #ffffff;
            --luxury-cream: #f8f6f0;
            --warm-white: #fafafa;
            --accent-copper: #d4a574;
            --accent-gold: #c9a96e;
            --deep-charcoal: #1d1d1f;
            --medium-gray: #86868b;
            --light-gray: #f5f5f7;
            --glass-white: rgba(255, 255, 255, 0.8);
            
            /* Effects */
            --shadow-luxury: 0 8px 32px rgba(0, 0, 0, 0.12);
            --shadow-copper: 0 4px 20px rgba(212, 165, 116, 0.3);
            --shadow-deep: 0 16px 64px rgba(0, 0, 0, 0.15);
            --border-radius: 12px;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            
            /* Typography */
            --font-primary: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-display: 'Playfair Display', serif;
        }
        
        body {
            font-family: var(--font-primary);
            background: var(--warm-white);
            color: var(--deep-charcoal);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
        }
        
        .luxury-navbar {
            background: var(--glass-white);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 0.5rem 0;
        }
        
        .navbar-brand {
            font-family: var(--font-display);
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--deep-charcoal) !important;
        }
        
        .nav-link {
            color: var(--deep-charcoal) !important;
            font-weight: 500;
            transition: var(--transition);
            margin: 0 0.25rem;
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem !important;
        }
        
        .nav-link:hover {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.1);
            transform: translateY(-1px);
        }
        
        .nav-link.active {
            color: var(--accent-copper) !important;
            background: rgba(212, 165, 116, 0.15);
            font-weight: 600;
        }
        
        .luxury-section {
            padding: 8rem 0;
            background: var(--luxury-cream);
        }
        
        .luxury-section:nth-child(even) {
            background: var(--warm-white);
        }
        
        .section-title {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 4rem);
            color: var(--deep-charcoal);
            text-align: center;
            margin-bottom: 4rem;
            font-weight: 300;
            letter-spacing: -0.02em;
        }
        
        .section-title .accent {
            color: var(--accent-copper);
            font-weight: 600;
        }
        
        .luxury-card {
            background: var(--primary-white);
            border: 1px solid rgba(212, 165, 116, 0.2);
            border-radius: var(--border-radius);
            padding: 3rem 2.5rem;
            transition: var(--transition);
            height: 100%;
            box-shadow: var(--shadow-luxury);
        }
        
        .contact-info-card {
            background: var(--primary-white);
            border: 1px solid rgba(212, 165, 116, 0.2);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--shadow-luxury);
            margin-bottom: 2rem;
        }
        
        .form-control {
            border: 1px solid rgba(212, 165, 116, 0.3);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-control:focus {
            border-color: var(--accent-copper);
            box-shadow: 0 0 0 0.2rem rgba(212, 165, 116, 0.25);
        }
        
        .cta-button {
            background: linear-gradient(135deg, var(--accent-copper), var(--accent-gold));
            color: var(--primary-white);
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            display: inline-block;
            box-shadow: var(--shadow-copper);
            border: none;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-deep);
            color: var(--primary-white);
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-copper));
        }
        
        .luxury-footer {
            background: var(--deep-charcoal);
            padding: 4rem 0 3rem;
            text-align: center;
            border-top: 1px solid rgba(212, 165, 116, 0.2);
        }
        
        .contact-icon {
            color: var(--accent-copper);
            margin-right: 1rem;
            font-size: 1.2rem;
        }
        
        .map-container {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-luxury);
            border: 1px solid rgba(212, 165, 116, 0.2);
        }
    </style>
</head>

<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg luxury-navbar fixed-top">
        <div class="container">
            <a class="navbar-brand" href="../index.html">
                Audio Excellence
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./music-systems.html">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-theatre.html">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./home-automation.html">Home Automation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="./contact.html">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="./showcase.html">Showcase</a>
                    </li>
                </ul>
                <a href="./contact.html" class="cta-button ms-3">Book a Demo</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="luxury-section" style="margin-top: 80px; background: linear-gradient(135deg, var(--luxury-cream), var(--warm-white)); min-height: 60vh; display: flex; align-items: center;">
        <div class="container text-center">
            <h1 class="section-title" data-aos="fade-up" style="margin-bottom: 1rem;">Drop Us a <span class="accent">Line</span></h1>
            <p class="lead" data-aos="fade-up" data-aos-delay="200" style="font-size: 1.4rem; color: var(--medium-gray); max-width: 700px; margin: 0 auto; line-height: 1.6;">We'd love to hear from you. Reach out for expert advice, a demo, or to start your project.</p>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="luxury-section">
        <div class="container">
            <div class="row g-5">
                <div class="col-lg-5" data-aos="fade-right">
                    <div class="contact-info-card">
                        <h3 style="color: var(--accent-copper); margin-bottom: 2rem; font-family: var(--font-display);">Contact Information</h3>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-phone contact-icon"></i>
                            <a href="tel:+27315665931" style="color: var(--deep-charcoal); text-decoration: none; font-weight: 500;">(*************</a>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-envelope contact-icon"></i>
                            <a href="mailto:<EMAIL>" style="color: var(--deep-charcoal); text-decoration: none; font-weight: 500;"><EMAIL></a>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <i class="fas fa-location-dot contact-icon"></i>
                            <span style="color: var(--deep-charcoal); font-weight: 500;">Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320</span>
                        </div>
                        
                        <div style="margin-bottom: 2rem;">
                            <i class="fas fa-clock contact-icon"></i>
                            <div style="color: var(--deep-charcoal);">
                                <strong>Business Hours:</strong><br>
                                Mon–Fri: 9am–5pm<br>
                                Sat: 9am–1pm<br>
                                Sun & Public Holidays: Closed
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <a href="https://facebook.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Facebook" style="color: var(--accent-copper); font-size: 1.5rem; margin-right: 1.5rem; transition: var(--transition);">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://instagram.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Instagram" style="color: var(--accent-copper); font-size: 1.5rem; transition: var(--transition);">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Map -->
                    <div class="map-container">
                        <div class="ratio ratio-16x9">
                            <iframe src="https://www.google.com/maps?q=Gateway+Office+Park,+1+Sugar+Close,+Umhlanga+Ridge,+4320&output=embed" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-7" data-aos="fade-left">
                    <div class="luxury-card">
                        <h3 style="color: var(--accent-copper); margin-bottom: 2rem; font-family: var(--font-display);">Send us a Message</h3>
                        
                        <form action="#" method="post" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="name" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback">Please enter your name.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback">Please enter a valid email address.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject" required>
                                <div class="invalid-feedback">Please enter a subject.</div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label" style="color: var(--deep-charcoal); font-weight: 500;">Message</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                                <div class="invalid-feedback">Please enter your message.</div>
                            </div>
                            
                            <button type="submit" class="cta-button" style="font-size: 1.1rem; padding: 1rem 2.5rem;">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="luxury-footer">
        <div class="container">
            <div style="max-width: 800px; margin: 0 auto;">
                <h3 style="font-family: var(--font-display); font-size: 2rem; color: var(--accent-copper); margin-bottom: 1.5rem; font-weight: 600;">Audio Excellence</h3>
                <p style="color: var(--light-gray); margin-bottom: 2rem; font-size: 1.1rem; line-height: 1.6;">
                    Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
                    <strong>(*************</strong> | 
                    <a href="mailto:<EMAIL>" style="color: var(--accent-copper); text-decoration: none;"><EMAIL></a>
                </p>
                
                <div style="margin-bottom: 2rem;">
                    <a href="https://facebook.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Facebook" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://instagram.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Instagram" style="color: var(--accent-copper); font-size: 1.75rem; margin: 0 1.5rem; transition: var(--transition);">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
                
                <p style="color: var(--light-gray); margin-top: 2rem; font-size: 0.95rem;">
                    © 2025 Audio Excellence Pty Ltd. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.luxury-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Bootstrap form validation
        (function() {
            'use strict';
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html>
