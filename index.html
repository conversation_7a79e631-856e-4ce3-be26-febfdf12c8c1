<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Excellence – Specialists in High End Audio, Home Theatre & Home Automation</title>
    <meta name="description" content="Audio Excellence: Specialists in High End Audio, Home Theatre & Home Automation. 30+ years of passion and expertise you can't get from an online store.">
    <meta name="keywords" content="High End Audio, Home Theatre, Home Automation, Luxury Audio, Audiophile, Cinema, Smart Home, Audio Excellence">
    <meta property="og:title" content="Audio Excellence – High End Audio, Home Theatre & Home Automation">
    <meta property="og:description" content="Experience luxury audio, home theatre, and automation with Audio Excellence. Visit our showroom.">
    <meta property="og:image" content="./image/Header-Home-Parallax.jpg">
    <meta property="og:type" content="website">
    <link rel="icon" type="image/png" href="./image/logo.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-black: #000000;
            --primary-white: #ffffff;
            --accent-orange: #FF4A00;
            --accent-orange-dark: #CC3A00;
            --dark-gray: #0a0a0a;
            --medium-gray: #1a1a1a;
            --light-gray: #2a2a2a;
            --text-light: #f5f5f5;
            --text-muted: #b0b0b0;
            --shadow-primary: 0 8px 32px rgba(0, 0, 0, 0.6);
            --shadow-orange: 0 4px 20px rgba(255, 74, 0, 0.3);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-black);
            color: var(--text-light);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Modern Navigation */
        .luxury-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 74, 0, 0.1);
            transition: var(--transition);
        }

        .luxury-navbar.scrolled {
            background: rgba(0, 0, 0, 0.95);
            box-shadow: var(--shadow-primary);
        }

        .navbar-brand {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent-orange) !important;
            text-decoration: none;
            letter-spacing: -0.02em;
        }

        .navbar-nav .nav-link {
            color: var(--text-light) !important;
            font-weight: 500;
            font-size: 1rem;
            padding: 0.75rem 1.5rem !important;
            border-radius: 8px;
            transition: var(--transition);
            position: relative;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: var(--accent-orange) !important;
            background: rgba(255, 74, 0, 0.1);
        }

        .cta-button {
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-dark));
            color: var(--primary-white) !important;
            padding: 0.75rem 2rem !important;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            box-shadow: var(--shadow-orange);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(255, 74, 0, 0.5);
            color: var(--primary-white) !important;
        }

        /* Hero Section */
        .hero-section {
            height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4)),
                        url('./assets/Header-Home-Parallax.jpg') center/cover no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            max-width: 900px;
            padding: 0 2rem;
            z-index: 2;
        }

        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary-white), var(--accent-orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: clamp(1.1rem, 2.5vw, 1.4rem);
            color: var(--text-muted);
            margin-bottom: 3rem;
            font-weight: 400;
        }

        .soundwave-container {
            margin: 2rem auto;
            max-width: 600px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Sections */
        .luxury-section {
            padding: 6rem 0;
            position: relative;
        }

        .luxury-section:nth-child(even) {
            background: var(--dark-gray);
        }

        .luxury-section:nth-child(odd) {
            background: var(--medium-gray);
        }

        .section-title {
            font-family: 'Playfair Display', serif;
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 600;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--accent-orange);
        }

        /* Cards */
        .luxury-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 2x2 Grid for Expertise Section */
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .luxury-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 74, 0, 0.1);
            border-radius: var(--border-radius);
            padding: 2.5rem 2rem;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .luxury-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 74, 0, 0.1), transparent);
            transition: left 0.6s;
        }

        .luxury-card:hover::before {
            left: 100%;
        }

        .luxury-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-orange);
            border-color: var(--accent-orange);
        }

        .card-icon {
            font-size: 3rem;
            color: var(--accent-orange);
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-white);
        }

        .card-description {
            color: var(--text-muted);
            line-height: 1.6;
        }

        /* Brand Carousel */
        .brand-section {
            background: var(--primary-black);
            padding: 4rem 0;
        }

        .brand-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            align-items: center;
            justify-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .brand-logo {
            height: 60px;
            width: auto;
            filter: grayscale(100%) brightness(0.7);
            transition: var(--transition);
            opacity: 0.8;
        }

        .brand-logo:hover {
            filter: none;
            opacity: 1;
            transform: scale(1.1);
        }

        /* Footer */
        .luxury-footer {
            background: var(--primary-black);
            padding: 3rem 0 2rem;
            text-align: center;
            border-top: 1px solid var(--light-gray);
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            color: var(--accent-orange);
            margin-bottom: 1rem;
        }

        .footer-contact {
            color: var(--text-muted);
            margin-bottom: 1.5rem;
        }

        .footer-social {
            margin-bottom: 2rem;
        }

        .footer-social a {
            color: var(--accent-orange);
            font-size: 1.5rem;
            margin: 0 1rem;
            transition: var(--transition);
        }

        .footer-social a:hover {
            color: var(--primary-white);
            transform: translateY(-2px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .luxury-cards {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .expertise-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .brand-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 1.5rem;
            }

            .luxury-section {
                padding: 4rem 0;
            }

            .navbar-nav .nav-link {
                padding: 0.5rem 1rem !important;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* Floating CTA */
        .floating-cta {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 999;
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-dark));
            color: var(--primary-white);
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            box-shadow: var(--shadow-orange);
            transition: var(--transition);
        }

        .floating-cta:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 32px rgba(255, 74, 0, 0.5);
            color: var(--primary-white);
        }
    </style>
</head>

<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg luxury-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                Audio Excellence
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/music-systems.html">Music Systems</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/home-theatre.html">Home Theatre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/home-automation.html">Home Automation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/contact.html">Contact</a>
                    </li>
                </ul>
                <a href="#contact" class="cta-button ms-3">Book a Demo</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="hero">
        <div class="hero-content" data-aos="fade-up" data-aos-duration="1000">
            <h1 class="hero-title">Specialists in High End Audio, Home Theatre & Home Automation</h1>
            <p class="hero-subtitle">30+ years of passion and expertise you can't get from an online store</p>

            <!-- Animated Soundwave -->
            <div class="soundwave-container" id="soundwave-hero">
                <svg width="100%" height="80" viewBox="0 0 600 80">
                    <g fill="none" stroke="#FF4A00" stroke-width="3" stroke-linecap="round">
                        <line x1="50" y1="40" x2="50" y2="40">
                            <animate attributeName="y2" values="40;10;40" dur="1.5s" repeatCount="indefinite"/>
                        </line>
                        <line x1="80" y1="40" x2="80" y2="40">
                            <animate attributeName="y2" values="40;20;40" dur="1.3s" repeatCount="indefinite" begin="0.1s"/>
                        </line>
                        <line x1="110" y1="40" x2="110" y2="40">
                            <animate attributeName="y2" values="40;5;40" dur="1.7s" repeatCount="indefinite" begin="0.2s"/>
                        </line>
                        <line x1="140" y1="40" x2="140" y2="40">
                            <animate attributeName="y2" values="40;25;40" dur="1.4s" repeatCount="indefinite" begin="0.3s"/>
                        </line>
                        <line x1="170" y1="40" x2="170" y2="40">
                            <animate attributeName="y2" values="40;15;40" dur="1.6s" repeatCount="indefinite" begin="0.4s"/>
                        </line>
                        <line x1="200" y1="40" x2="200" y2="40">
                            <animate attributeName="y2" values="40;8;40" dur="1.2s" repeatCount="indefinite" begin="0.5s"/>
                        </line>
                        <line x1="230" y1="40" x2="230" y2="40">
                            <animate attributeName="y2" values="40;30;40" dur="1.8s" repeatCount="indefinite" begin="0.6s"/>
                        </line>
                        <line x1="260" y1="40" x2="260" y2="40">
                            <animate attributeName="y2" values="40;12;40" dur="1.5s" repeatCount="indefinite" begin="0.7s"/>
                        </line>
                        <line x1="290" y1="40" x2="290" y2="40">
                            <animate attributeName="y2" values="40;18;40" dur="1.3s" repeatCount="indefinite" begin="0.8s"/>
                        </line>
                        <line x1="320" y1="40" x2="320" y2="40">
                            <animate attributeName="y2" values="40;6;40" dur="1.7s" repeatCount="indefinite" begin="0.9s"/>
                        </line>
                        <line x1="350" y1="40" x2="350" y2="40">
                            <animate attributeName="y2" values="40;22;40" dur="1.4s" repeatCount="indefinite" begin="1.0s"/>
                        </line>
                        <line x1="380" y1="40" x2="380" y2="40">
                            <animate attributeName="y2" values="40;14;40" dur="1.6s" repeatCount="indefinite" begin="1.1s"/>
                        </line>
                        <line x1="410" y1="40" x2="410" y2="40">
                            <animate attributeName="y2" values="40;28;40" dur="1.2s" repeatCount="indefinite" begin="1.2s"/>
                        </line>
                        <line x1="440" y1="40" x2="440" y2="40">
                            <animate attributeName="y2" values="40;16;40" dur="1.8s" repeatCount="indefinite" begin="1.3s"/>
                        </line>
                        <line x1="470" y1="40" x2="470" y2="40">
                            <animate attributeName="y2" values="40;10;40" dur="1.5s" repeatCount="indefinite" begin="1.4s"/>
                        </line>
                        <line x1="500" y1="40" x2="500" y2="40">
                            <animate attributeName="y2" values="40;24;40" dur="1.3s" repeatCount="indefinite" begin="1.5s"/>
                        </line>
                        <line x1="530" y1="40" x2="530" y2="40">
                            <animate attributeName="y2" values="40;12;40" dur="1.7s" repeatCount="indefinite" begin="1.6s"/>
                        </line>
                    </g>
                </svg>
            </div>
        </div>
    </section>

    <!-- Floating CTA -->
    <a href="#contact" class="floating-cta">Book a Demo</a>

    <!-- Our Expertise Section -->
    <section class="luxury-section" id="expertise">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Our Expertise</h2>
            <div class="expertise-grid px-3">
                <div class="luxury-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="card-title">High-End Audio</h3>
                    <p class="card-description">With our specially selected music playback systems, you can relive the music of your favourite bands and artists.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-film"></i>
                    </div>
                    <h3 class="card-title">Home Theatre</h3>
                    <p class="card-description">Bring the thrill of the Silver Screen into your home with your own private cinema.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3 class="card-title">Home Automation</h3>
                    <p class="card-description">Add comfort, convenience, security, and energy efficiency to your lifestyle.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="card-title">Office & Hospitality</h3>
                    <p class="card-description">AV and control systems for boardrooms, office spaces, and restaurants.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Customised Solutions -->
    <section class="luxury-section" id="solutions">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Customised Solutions</h2>
            <div class="luxury-cards px-3">
                <div class="luxury-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="card-title">Tailored Design</h3>
                    <p class="card-description">Every system is designed for your space and needs. Modern layouts with premium finishes.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="card-title">Security & Reliability</h3>
                    <p class="card-description">We use only the most reliable, high-quality products and installation practices.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="card-title">Smart Integration</h3>
                    <p class="card-description">Seamless integration of lighting, AV, security, blinds, and more.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quality Promise -->
    <section class="luxury-section" id="quality">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up">Highest Quality Products</h2>
            <div class="luxury-cards px-3">
                <div class="luxury-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3 class="card-title">Award-Winning Brands</h3>
                    <p class="card-description">We partner with the world's most respected audio and automation brands.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3 class="card-title">Certified Installers</h3>
                    <p class="card-description">Our team is factory-trained and certified for every product we install.</p>
                </div>

                <div class="luxury-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="card-title">Unmatched Support</h3>
                    <p class="card-description">We provide ongoing support and service for every system we deliver.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Showroom Invitation -->
    <section class="luxury-section" id="showroom" style="background: linear-gradient(135deg, var(--medium-gray), var(--dark-gray)); text-align: center;">
        <div class="container">
            <div data-aos="zoom-in" style="max-width: 800px; margin: 0 auto; padding: 3rem 2rem; background: rgba(255, 74, 0, 0.1); border-radius: var(--border-radius); border: 1px solid var(--accent-orange);">
                <h2 style="font-family: 'Playfair Display', serif; font-size: 2.5rem; color: var(--accent-orange); margin-bottom: 1rem;">Experience the Difference</h2>
                <p style="font-size: 1.3rem; color: var(--text-light); margin-bottom: 2rem;">This is how music & movies should be experienced…</p>
                <a href="pages/contact.html" class="cta-button" style="font-size: 1.2rem; padding: 1rem 3rem;">Visit Our Showroom</a>
            </div>
        </div>
    </section>

    <!-- Brand Partners -->
    <section class="brand-section" id="brands">
        <div class="container">
            <h2 class="section-title" data-aos="fade-up" style="color: var(--accent-orange);">Our Premium Partners</h2>
            <div class="brand-grid" data-aos="fade-up" data-aos-delay="200">
                <img src="./brands/logo-brand-bluesound.jpg" alt="Bluesound" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-clearaudio-1.jpg" alt="Clearaudio" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-conrad-johnson-1.jpg" alt="Conrad-Johnson" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-control4.jpg" alt="Control4" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-crestron.jpg" alt="Crestron" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-epson.jpg" alt="Epson" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-golden-ear-tech-1.jpg" alt="GoldenEar Technology" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-isotek.jpg" alt="IsoTek" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-kef.jpg" alt="KEF" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-klipsch.jpg" alt="Klipsch" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-magico.jpg" alt="Magico" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-mcintosh.jpg" alt="McIntosh" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-mission.jpg" alt="Mission" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-nad.jpg" alt="NAD" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-primare.jpg" alt="Primare" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-pro-ject.jpg" alt="Pro-Ject" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-rotel.jpg" alt="ROTEL" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-supra-cables.jpg" alt="SUPRA Cables" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-svs-sound-revolution.jpg" alt="SVS" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-wharfedale.jpg" alt="Wharfedale" class="brand-logo" loading="lazy">
                <img src="./brands/logo-brand-yamaha.jpg" alt="Yamaha" class="brand-logo" loading="lazy">
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="luxury-footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <h3 class="footer-title">Audio Excellence</h3>
                <p class="footer-contact">
                    Gateway Office Park, 1 Sugar Close, Umhlanga Ridge, 4320<br>
                    <strong>(*************</strong> |
                    <a href="mailto:<EMAIL>" style="color: var(--accent-orange); text-decoration: none;"><EMAIL></a>
                </p>

                <div class="footer-social">
                    <a href="https://facebook.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://instagram.com/audioexcellenceza" target="_blank" rel="noopener" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>

                <p style="color: var(--text-muted); margin-top: 2rem;">
                    © 2025 Audio Excellence Pty Ltd. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.luxury-navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>

</html>