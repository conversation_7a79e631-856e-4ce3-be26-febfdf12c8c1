Source Sans 3 Variable Font
===========================

This download contains Source Sans 3 as both variable fonts and static fonts.

Source Sans 3 is a variable font with this axis:
  wght

This means all the styles are contained in these files:
  SourceSans3-VariableFont_wght.ttf
  SourceSans3-Italic-VariableFont_wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Source Sans 3:
  static/SourceSans3-ExtraLight.ttf
  static/SourceSans3-Light.ttf
  static/SourceSans3-Regular.ttf
  static/SourceSans3-Medium.ttf
  static/SourceSans3-SemiBold.ttf
  static/SourceSans3-Bold.ttf
  static/SourceSans3-ExtraBold.ttf
  static/SourceSans3-Black.ttf
  static/SourceSans3-ExtraLightItalic.ttf
  static/SourceSans3-LightItalic.ttf
  static/SourceSans3-Italic.ttf
  static/SourceSans3-MediumItalic.ttf
  static/SourceSans3-SemiBoldItalic.ttf
  static/SourceSans3-BoldItalic.ttf
  static/SourceSans3-ExtraBoldItalic.ttf
  static/SourceSans3-BlackItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
