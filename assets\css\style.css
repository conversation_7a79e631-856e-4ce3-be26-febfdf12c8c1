/* Table of Contents:
1. Import Statements
2. Keyframes and Property Animations    
3. Custom Properties
4. Global Styles
5. Typography Styles
6. Heading and Subheadings
7. Form and Inputs
8. Maps Elements
9. Header and Navbar
10. Section and Container
11. Background and Setting Color
12. Swiper Setting
13. Button and Links
14. Overlay
15. Utility Classes
16. Social and Contact Setting
17. Breadcrumb
18. Spesific Media Queries
19. Card Setting
20. Progress and Rating 
21. Accordion
*/

/* ---------------------------- */
/* Import Statements            */
/* ---------------------------- */
@import url('../css/vendor/');
@import url('../css/vendor/bootstrap.min.css');
@import url('../css/vendor/fontawesome.css');
@import url('../css/vendor/brands.css');
@import url('../css/vendor/regular.css');
@import url('../css/vendor/solid.css');
@import url('../css/vendor/swiper-bundle.min.css');
@import url('../css/vendor/rtmicons.css');
@import url('../css/vendor/open-sans/stylesheet.css');
@import url('../css/vendor/source-sans-3/stylesheet.css');


/* ---------------------------- */
/* Keyframes and Property Animations            */
/* ---------------------------- */
@property --progress {
    syntax: '<integer>';
    inherits: true;
    initial-value: 0;
}

@keyframes load {
    to {
        --progress: var(--value)
    }
}

@keyframes background_animation {
    from {
        background-size: 100%;
    }

    to {
        background-size: 110%;
    }
}

@keyframes ripple {
    from {
        opacity: 1;
        transform: scale3d(1, 1, 1);
        transform-origin: center;
        border-width: 0px;
    }

    to {
        opacity: 0;
        transform: scale3d(1.7, 1.7, 1.8);
        transform-origin: center;
        border-width: 13px;
    }
}

/* ---------------------------- */
/*  Custom Properties            */
/* ---------------------------- */
:root {
    --primary: #000000;
    --text-color: #FFFFFF;
    --text-color-2: #F4F4F4;
    --background-color: #000000;
    --accent-color: #FF4A00;
    --accent-color-2: #CC3A00;
    --accent-color-3: #1A1A1A;
    --accent-color-4: #0A0A0A;
    --font-1: "Source Sans 3", sans-serif;
    --font-2: "Open Sans", sans-serif;
}


/* ---------------------------- */
/* Global Styles                */
/* ---------------------------- */
body {
    font-family: var(--font-1);
    color: var(--text-color);
    background-color: var(--primary);
}


/* ---------------------------- */
/* Typography                   */
/* ---------------------------- */
h1 {
    font-size: 84px;
    font-weight: bold;
}

h2 {
    font-size: 80px;
    font-weight: bold;
}

h3 {
    font-size: 48px;
    font-weight: bold;
}

h4 {
    font-size: 32px;
    font-weight: bold;
}

h5 {
    font-size: 24px;
    font-weight: 500;
}

h6 {
    font-size: 20px;
}

button,
a {
    font-size: 18px;
    font-family: var(--font-2);
}

p {
    font-size: 16px;
    font-family: var(--font-2);
    color: var(--text-color-2);
}

ul {
    list-style: none;
}

.list-circle {
    list-style: disc var(--accent-color);
}

li {
    font-size: 16px;
}

img {
    object-fit: cover;
}

.text-accent {
    background: linear-gradient(90deg, #FF4A00 0%, #CC3A00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.price-text {
    font-size: 64px;
    font-weight: bolder;
}

.price-text2 {
    font-size: 40px;
    font-weight: bolder;
}

.testimonial-text {
    font-size: 12px;
    font-style: italic;
}

.small-text {
    font-size: 12px;
    font-family: var(--font-2);
}

.brush1 {
    position: relative;
    display: inline-block;
    padding: 5px 0px;
}

.brush1::before {
    content: "";
    background: url('../image/brush1.png') no-repeat center;
    background-size: contain;
    position: absolute;
    width: 110%;
    height: 100%;
    top: 0;
    left: -5%;
    z-index: -1;
    opacity: 0.8;
}

.brush2 {
    position: relative;
    display: inline-block;
    padding: 5px 0px;
}

.brush2::before {
    content: "";
    background: url('../image/brush2.png') no-repeat center;
    background-size: contain;
    position: absolute;
    width: 110%;
    height: 100%;
    top: 0;
    left: -5%;
    z-index: -1;
    opacity: 0.8;
}

.brush3 {
    position: relative;
    display: inline-block;
    padding: 5px 0px;
}

.brush3::before {
    content: "";
    background: url('../image/brush3.png') no-repeat center;
    background-size: contain;
    position: absolute;
    width: 110%;
    height: 100%;
    top: 0;
    left: -5%;
    z-index: -1;
    opacity: 0.8;
}

.brush4 {
    position: relative;
    display: inline-block;
    padding: 5px 0px;
}

.brush4::before {
    content: "";
    background: url('../image/brush4.png') no-repeat center;
    background-size: contain;
    position: absolute;
    width: 110%;
    height: 100%;
    top: 0;
    left: -5%;
    z-index: -1;
    opacity: 0.8;
}

.brush5 {
    position: relative;
    display: inline-block;
    padding: 5px 0px;
}

.brush5::before {
    content: "";
    background: url('../image/brush5.png') no-repeat center;
    background-size: contain;
    position: absolute;
    width: 110%;
    height: 100%;
    top: 0;
    left: -5%;
    z-index: -1;
    opacity: 0.8;
}

/* ---------------------------- */
/* Headings and Subheadings      */
/* ---------------------------- */
.banner-heading {
    font-size: 5.5rem;
}

.sub-heading {
    color: var(--accent-color);
}

.text-color {
    color: var(--text-color);
}

.text-color-2 {
    color: var(--text-color-2);
}

.p-banner {
    color: var(--primary);
}

/* ---------------------------- */
/* Forms and Inputs             */
/* ---------------------------- */
.form-control {
    padding: 15px 24px;
    border: none;
}

.form-label {
    font-size: 24px;
}

.form textarea {
    background-color: var(--accent-color-3);
    border-radius: 0;
    color: var(--text-color-2);
    outline: none;
    font-family: var(--font-2);
}

.form input,
.form select {
    background-color: var(--accent-color-3);
    border-radius: 0;
    color: var(--text-color-2);
    outline: none;
    font-family: var(--font-2);
}


.form input:focus,
.form textarea:focus,
.form select:focus {
    box-shadow: none;
    border: solid 1px var(--accent-color);
    background-color: var(--accent-color-3);
    color: var(--text-color-2);
}

.form input:autofill,
.form input:autofill:focus {
    color: var(--text-color-2);
    transition: background-color 5000s ease-in-out;
    -webkit-text-fill-color: var(--text-color-2);
    font-family: var(--font-2);
}

.form input::placeholder,
.form textarea::placeholder {
    color: var(--text-color-2);
    font-family: var(--font-2);
}

.form option {
    background-color: var(--primary);
}

.form-check-input:checked[type=checkbox] {
    --bs-form-check-bg-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="blue" class="bi bi-check-xl" viewBox="0 0 16 16"><path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/></svg>');
}

.form input.form-check-input {
    background-color: transparent;
    border: 1px solid gray;
}

.form input.form-check-input:checked {
    border: 1px solid blue;
    color: blue;
}

.form .submit_form {
    padding-inline: 2rem;
    padding-block: 1rem;
    text-decoration: none;
    transition: all 0.5s;
    background-color: var(--accent-color);
    border-radius: 5px;
}


.submit_form:hover {
    background-color: transparent;
    color: var(--accent-color);
}


.submit_appointment {
    padding-inline: 3rem;
    padding-block: 0.7rem;
    text-decoration: none;
}


/* ---------------------------- */
/* Map Elements       */
/* ---------------------------- */
.maps {
    width: 100%;
    height: 480px;
    transition: filter 0.5s;
    display: block;
}


/* ---------------------------- */
/* Header and Navbar       */
/* ---------------------------- */
#header {
    transition: all 0.5s ease;
}

.logo-container {
    max-width: 200px;
}

.logo-partner {
    filter: brightness(200%) contrast(0%) saturate(0%) blur(0px) hue-rotate(0deg);
    transition-duration: 0.5s;
}

.logo-partner:hover {
    filter: none;
}

.offcanvas {
    background-color: var(--primary);
}

.offcanvas-header {
    color: var(--text-color);
}

.navbar-nav .nav-link:focus {
    color: var(--accent-color);
    text-align: center;
}

.navbar-nav .nav-link.show {
    color: var(--accent-color);
}

.nav-link {
    border-bottom: 2px solid transparent;
    font-size: 18px;
    font-family: var(--font-2);
    color: var(--text-color-2);
    text-align: center;
}

.nav-link:hover {
    color: var(--accent-color);
    text-align: center;
}

.nav-link.active {
    color: var(--text-color) !important;
    text-align: center;
    border-bottom: 2px solid var(--accent-color);
}

.navbar-toggler {
    border: none;
    color: var(--accent-color-2);
}

.navbar-toggler:focus {
    box-shadow: none;
    background-color: transparent;
    color: var(--accent-color-2);
}

.nav-tabs {
    border-bottom: none;
}

.nav-tabs .nav-link {
    background-color: transparent;
    color: var(--accent-color);
    border: none;
    position: relative;
}

.nav-tabs .nav-link:hover {
    border: none;
    color: white;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border: none;
}

.nav-tabs .nav-link.active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-color);
}

.dropdown-menu {
    border-radius: 0;
    border: none;
    padding: 0;
    width: 200px;
    -webkit-box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
    padding-block: 0.75rem;
    color: var(--text-color);
    font-family: var(--font-1);
    font-size: 0.95rem;
    font-weight: 400;
    padding-inline: 0.75rem;
    text-align: center;
    background-color: var(--background-color);
}

.dropdown-item.active {
    color: var(--primary);
    background-color: var(--accent-color);
}

.dropdown-item:hover {
    background-color: var(--accent-color);
    color: var(--primary);
}

.dropdown-item:focus {
    color: var(--accent-color);
}

/* ---------------------------- */
/* Section and Container       */
/* ---------------------------- */
.section {
    padding: 6em 2em 6em 2em;
    overflow: hidden;
    background-size: cover;
    background-position: center;
}

.r-container {
    max-width: 1440px;
    margin-right: auto;
    margin-left: auto;
}


/* ---------------------------- */
/* Background and Setting Color      */
/* ---------------------------- */
.bg-accent-primary {
    background-color: var(--primary);
}

.bg-accent-color {
    background-color: var(--accent-color);
}

.bg-accent-color-2 {
    background-color: var(--accent-color-2);
}

.bg-accent-color-3 {
    background-color: var(--accent-color-3);
}

.bg-accent-color-4 {
    background-color: var(--accent-color-4);
}

.bg-accent {
    background-color: var(--background-color);

}

.bg-text-color {
    background-color: var(--text-color);
}

.bg-text-color-2 {
    background-color: var(--text-color-2);
}

.bg-accent-color-hover:hover {
    background-color: var(--accent-color);
    color: white;
}

.bg-dark-transparent {
    background-color: #232323b7;
}

.accent-color-primary {
    color: var(--primary);
}

.accent-color {
    color: var(--accent-color);
}

.accent-color-2 {
    color: var(--accent-color-2);
}

.accent-color-3 {
    color: var(--accent-color-3);
}

.accent-color-4 {
    color: var(--accent-color-4);
}

.accent {
    color: var(--background-color);
}

.border-accent {
    border-color: var(--text-color-2) !important;
}

.border-accent-2 {
    border-color: var(--accent-color) !important;
}

.border-accent-3 {
    border: 1px solid rgba(207, 171, 130, 0.2);
}

.border-text-color {
    border-color: var(--text-color) !important;
}

.border-testimonial {
    border-right: 5px solid var(--accent-color-2);
}

.border-bottom-hover:hover {
    border-bottom: 2px solid var(--accent-color);
}

.border-accent-color {
    border: 1px solid var(--accent-color);
}

.custom-border {
    padding: 2rem 3rem;
    border-radius: 1rem;
}

.custom-border:hover {
    box-shadow: 0px 5px 6px rgba(1, 199, 243, 0.26);
}



.outline {
    color: transparent;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: var(--accent-color);
}

.text-gray {
    color: grey !important;
}

/* ---------------------------- */
/* Swiper Setting              */
/* ---------------------------- */
.text-marquee {
    overflow: hidden;
    white-space: nowrap;
    position: relative;
}

.text-marquee-content {
    display: inline-block;
    animation: text-marquee 70s linear infinite;
    font-size: 100px;
    padding-right: 50%;
}

@keyframes text-marquee {
    from {
        transform: translateX(0%);
    }

    to {
        transform: translateX(-100%);
    }
}

.marquee-container {
    overflow: hidden;
    /* white-space: nowrap; */
    width: 100%;
    display: flex;
    flex-direction: row;
    --gap: 1rem;
    --speed: 20;
    /* gap: var(--gap); */
}

.marquee {
    animation: marquee calc(500s / var(--speed)) infinite linear;
}

.reverse .marquee {
    animation-direction: reverse;
}

.marquee-content {
    display: inline-flex;
}

.marquee-item:hover img {
    filter: var(--accent-color);
}

.marquee-item {
    text-wrap: nowrap;
    padding-inline: var(--gap);
    margin-right: 1rem;
    width: max-content;
}

@keyframes marquee {
    from {
        transform: translateX(0%);
    }

    to {
        transform: translateX(calc(-100% - 1rem));
    }
}

.service-scroll {
    height: 610px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 3rem;
}

/* width */
.service-scroll::-webkit-scrollbar {
    width: 6px;
}

/* Track */
.service-scroll::-webkit-scrollbar-track {
    background: #e0e0e0;
    border-radius: 10px;
}

/* Handle */
.service-scroll::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 10px;
}

/* Handle on hover */
.service-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color-2);
}

.swiperTestimonials {
    overflow: visible;
}

.swiperTestimonials2 {
    position: relative;
    padding-bottom: 40px;
}

.swiperStep .swiper-slide.swiper-slide-thumb-active h4 {
    color: var(--accent-color);
}

.swiperStep {
    width: 100%;
    height: 300px;
    margin-left: auto;
    margin-right: auto;
}

.swiperStep2 .swiper-slide img {
    display: block;
    width: 100%;
    height: 550px;
    object-fit: cover;
}

.swiperStep2 .swiper-slide {
    background-size: cover;
    background-position: center;
}

.swiperStep {
    width: 100%;
    height: 100%;
}

.swiperStep .swiper-slide img {
    display: block;
    width: 100%;
    object-fit: cover;
}

.swiperStep {
    height: 20%;
    box-sizing: border-box;
    padding: 10px 0;
}

.swiperStep .swiper-slide {
    width: 25%;
    height: 100%;
    opacity: 0.4;
}

.swiperStep .swiper-slide-thumb-active {
    opacity: 1;
}

.mySwiper {
    position: relative;
    padding-bottom: 156px;
}

.swiperImage {
    position: relative;
    width: 100%;
}

.swiper-pagination {
    margin-block: 1rem;
    position: relative;
}


.swiper-pagination-bullet {
    width: 30px;
    height: 3px;
    background: var(--accent-color-3);
    margin: 0 5px;
    border-radius: 0;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    background: var(--accent-color);
    width: 40px;
    opacity: 1;
}

.swiper-slide {
    padding: 0.5rem;
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 27px;
}

.swiperStep2 .swiper-button-next::after,
.swiperStep2 .swiper-button-prev::after {
    font-size: 45px;
}

.swiperStep2 .swiper-button-next,
.swiperStep2 .swiper-button-prev {
    right: -1rem;
    top: 15rem;
    width: 95px;
    height: 95px;
    border: 6px solid var(--primary);
    background: linear-gradient(180deg, #050505 -77.42%, #01C7F3 100%);
    color: var(--text-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

/* Center the buttons horizontally */
.swiperStep2 .swiper-button-next {
    right: 0;
    transform: translateY(20px);
}

.swiperStep2 .swiper-button-prev {
    left: 0;
    transform: translateY(20px);
}

.mySwiper .swiper-button-next,
.mySwiper .swiper-button-prev {
    top: 25rem;
    width: 50px;
    height: 50px;
    border: 2px solid var(--accent-color);
    background-color: transparent;
    color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

/* Center the buttons horizontally */
.mySwiper .swiper-button-next {
    right: 45%;
    transform: translateY(20px);
}

.mySwiper .swiper-button-prev {
    left: 45%;
    transform: translateY(20px);
}

/* Center the buttons horizontally */
.swiper-button-next.service {
    right: -9%;
    /* Gerakkan tombol next ke kanan dari tengah */
    transform: translateY(20px);
}

.swiper-button-prev.service {
    left: -7%;
    /* Gerakkan tombol prev ke kiri dari tengah */
    transform: translateY(20px);
}

/* Style for both next and prev buttons */
.swiperImage .swiper-button-next,
.swiperImage .swiper-button-prev {
    bottom: 10px;
    /* Posisikan tombol di bagian bawah */
    width: 50px;
    height: 50px;
    border: 1px solid var(--accent-color);
    background-color: var(--background-color);
    /* Warna background semi-transparan */
    color: var(--accent-color);
    border-radius: 50%;
    /* Membuat tombol bulat */
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

/* Center the buttons horizontally */
.swiperImage .swiper-button-next {
    left: 50%;
    /* Gerakkan tombol next ke kanan dari tengah */
    transform: translateY(20px);
}

.swiperImage .swiper-button-prev {
    left: 47%;
    /* Gerakkan tombol prev ke kiri dari tengah */
    transform: translateY(20px);
}

/* Ukuran icon panah */
.swiperImage .swiper-button-next::after,
.swiperImage .swiper-button-prev::after {
    font-size: 24px;
}

/* Hover effect */
.swiperImage .swiper-button-next:hover,
.swiperImage .swiper-button-prev:hover {
    background-color: var(--accent-color);
    color: var(--primary);
    /* Background lebih gelap saat hover */
}

.custom-swiper {
    width: 100%;
    height: 600px;
    padding-right: 3rem;
}

.custom-slide {
    display: flex;
    justify-content: center;
    align-items: center;
}

.custom-scrollbar {
    width: 6px;
    position: absolute;
    right: 10px;
    top: 0px;
    margin-top: 150px;
    bottom: 0;
    background: #e0e0e0;
    border-radius: 10px;
    z-index: 9999;
}

.swiper-scrollbar.swiper-scrollbar-vertical {
    height: 300px;
}

.custom-scrollbar .swiper-scrollbar-drag {
    background: #ff9800;
    border-radius: 10px;
}

/* ---------------------------- */
/* Buttons & Links              */
/* ---------------------------- */
button {
    padding-inline: 1rem;
    padding-block: 0.5rem;
    text-decoration: none;
    transition: all 0.5s;
}

button:hover {
    color: var(--text-color);
}

a {
    text-decoration: none;
}

.w-max-content {
    width: max-content;
}

.read-more {
    color: var(--text-color-2);
    font-family: var(--font-2);
    font-size: 16px;
    transition: all 0.5s;
}

.read-more.blog {
    color: var(--text-color);
    transition: all 0.5s;
}

.read-more:hover {
    color: var(--accent-color);
}

.tags {
    padding: 3px 6px;
    font-family: var(--font-2);
    color: var(--text-color);
    background-color: var(--accent-color-3);
}

.tags.active {
    color: var(--primary);
    background-color: var(--accent-color);
}

.btn {
    font-size: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-2);
    font-weight: 600;
    position: relative;
	display: inline-block;
    pointer-events: auto;
	cursor: pointer;

}

.btn::before,
.btn::after {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.btn-close {
    color: var(--text-color);
}

.btn-close:hover {
    color: var(--text-color);
}

.btn-accent {
    background: var(--accent-color);
    color: var(--primary);
    transition: all 0.5s;
    border: 1px solid transparent;
    overflow: hidden;
}

.btn-accent:hover {
    color: var(--primary);
}

.btn-accent:hover i{
    color: var(--primary);
}

.btn-accent span {
	display: block;
	position: relative;
	z-index: 10;
}

.btn-accent:hover i {
	animation: MoveScaleUpInitial 0.3s forwards, MoveScaleUpEnd 0.3s forwards 0.3s;
}

.btn-accent i {
	display: block;
	position: relative;
	z-index: 10;
}

.btn-accent:hover span {
	animation: MoveScaleUpInitial 0.3s forwards, MoveScaleUpEnd 0.3s forwards 0.3s;
}

@keyframes MoveScaleUpInitial {
	to {
		transform: translate3d(0,-105%,0) scale3d(1,2,1);
		opacity: 0;
	}
}

@keyframes MoveScaleUpEnd {
	from {
		transform: translate3d(0,100%,0) scale3d(1,2,1);
		opacity: 0;
	}
	to {
		transform: translate3d(0,0,0);
		opacity: 1;
	}
}

.btn-accent::before {
	content: '';
	background: var(--accent-color);
	width: 120%;
	height: 0;
	padding-bottom: 120%;
	top: -110%;
	left: -10%;
	border-radius: 50%;
	transform: translate3d(0,68%,0) scale3d(0,0,0);
}

.btn-accent:hover::before {
	transform: translate3d(0,0,0) scale3d(1,1,1);
	transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
}

.btn-accent::after {
	content: '';
	background: var(--accent-color);
	transform: translate3d(0,-100%,0);
	transition: transform 0.4s cubic-bezier(0.1, 0, 0.3, 1);
}

.btn-accent:hover::after {
	transform: translate3d(0,0,0);
	transition-duration: 0.05s;
	transition-delay: 0.4s;
	transition-timing-function: linear;
}

.btn-accent.accent:hover {
    background-color: transparent;
    border-color: var(--primary);
    color: var(--primary);
}

.btn-accent-outline {
    background-color: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
    overflow: hidden;
}

.btn-accent-outline:hover {
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
}

.btn-accent-outline span {
	display: block;
	position: relative;
	z-index: 10;
}

.btn-accent-outline:hover span {
	animation: MoveScaleUpInitial 0.3s forwards, MoveScaleUpEnd 0.3s forwards 0.3s;
}

.btn-accent-underline::before {
    transform-origin: 0% 50%;
    transform: scale3d(0, 1, 1);
    transition: transform 0.3s;
}

.btn-accent-underline:hover::before {
    transform: scale3d(1, 1, 1);
}

.btn-accent-underline::after {
    content: '';
    top: calc(100% + 4px);
    transition: transform 0.3s;
    transform-origin: 100% 50%;
}

.btn-accent-underline::before,
.btn-accent-underline::after {
    position: absolute;
    width: 100%;
    height: 1px;
    background: currentColor;
    top: 100%;
    left: 0;
    pointer-events: none;
    
}

.btn-accent-underline:hover::after {
    transform: scale3d(0, 1, 1);
}

.btn-accent-underline:hover {
    color: var(--text-color);
}

.btn-accent-underline {
    background-color: transparent;
    border-bottom: 1px solid var(--accent-color);
    color: var(--accent-color);
    white-space: nowrap;
    cursor: pointer;
    position: relative;
}

.btn-toggler-accent {
    background-color: transparent;
    border: 1px solid var(--text-color);
    color: var(--text-color);
    aspect-ratio: 1/1;
    width: 60px;
    height: 60px;
    border-radius: 50%;
}


.btn-toggler-accent:hover {
    background-color: var(--text-color);
    color: var(--primary);
}

.btn-white-outline {
    background-color: transparent;
    border-color: var(--text-color);
    color: var(--text-color);
    border-width: 1px;
}

.btn-white-outline-hover:hover {
    background-color: transparent;
    border-color: var(--text-color);
    color: var(--text-color);
}

.btn-white-outline:hover {
    background-color: white;
    color: var(--primary);
}

.partner {
    display: flex;
    justify-content: center;
    padding: 16px 24px;
    border: 1px solid transparent;
}

.partner:hover {
    border: 1px solid var(--accent-color);
}

.partner svg {
    fill: var(--accent-color);
}

.partner:hover svg {
    fill: var(--accent-color);
}

.image-footer .social-item {
    color: var(--primary);
    background-color: var(--accent-color);
}

.image-footer:hover .image-footer-blur {
    background: rgba(24, 21, 24, 0.3);
    backdrop-filter: blur(2px);
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1;
    transition: all 0.5s ease;
}

.image-footer {
    position: relative;
    width: max-content;
}

.image-footer .image-zoom {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;
    transform-origin: bottom;
    transition: all 0.5s ease;
}

.image-footer:hover .image-zoom {
    opacity: 1;
}

.card-track:hover .track-blur {
    opacity: 1;
    transform: translate(1rem, -1rem);
}

.card-track .track-blur {
    opacity: 0;

}

.track-blur {
    transition: all 0.5s ease;
    position: absolute;
    bottom: 0;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: visible;
}

.card-track {
    transition: all 0.5s ease;
}

.card .link {
    color: var(--accent-color);
    transition: color 0.5s;
}

.card .link:hover {
    color: var(--primary);
}

.link.accent-color {
    color: var(--accent-color);
    transition: color 0.5s;
}

.link.accent-color:hover {
    color: var(--dark-bg);
}

.link {
    color: var(--text-color);
    transition: color 0.5s;
}

.link:hover {
    color: var(--accent-color);
}

.link-white {
    color: white;
}

.link-white:hover {
    color: var(--accent-color);
}

/* ---------------------------- */
/* Overlay                      */
/* ---------------------------- */
.blog-overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 69, 81, 0.6) 100%);
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
}

.image-overlay {
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    position: absolute;
    background: linear-gradient(180deg, rgba(5, 5, 5, 0.3) 23.17%, rgba(1, 199, 243, 0.3) 127.38%);
}

.overlay {
    color: var(--accent-color-2);
    opacity: 0.3;
}

.bg-overlay {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5));
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;

}

.bg-overlay-2 {
    background: linear-gradient(0deg, rgba(1, 137, 142, 0.28), rgba(1, 137, 142, 0.28));
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.testimonial-overlay {
    background: linear-gradient(0deg, rgba(255, 239, 225, 0.96), rgba(255, 239, 225, 0.96));
    position: absolute;
    width: 75%;
    height: 100%;
    top: 0;
    right: 0;
}

.bg-accent-opacity {
    background: linear-gradient(0deg, rgba(43, 43, 43, 0.86), rgba(43, 43, 43, 0.86));
}

.bg-blur {
    position: absolute;
    background: rgba(24, 21, 24, 0.3);
    border: 1px solid rgba(207, 171, 130, 0.2);
    backdrop-filter: blur(17.5px);
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1;
}

.linear-gradient {
    padding: 14px 42px;
    background-color: var(--accent-color-2);
    border-radius: 50px;
    width: max-content;
}

.cta-overlay {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 20.5%, rgba(1, 199, 243, 0.2) 100%);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.contact-overlay {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 27.06%, rgba(1, 199, 243, 0.61) 100%);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;

}

.video-overlay {
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    position: absolute;
    background: linear-gradient(180deg, rgba(4, 56, 63, 0.144) 0%, rgba(4, 56, 63, 0.72) 100%);
    opacity: 0.5;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
}

/* ---------------------------- */
/* Utility Classes              */
/* ---------------------------- */
.hover-transform:hover {
    transform: translateY(-10px);
}

.font-1 {
    font-family: var(--font-1);
}

.font-2 {
    font-family: var(--font-2);
}

.font-3 {
    font-family: var(--font-3);
}

.ls-2 {
    letter-spacing: 2px;
}

.fs-7 {
    font-size: 0.8rem !important;
}

.fs-very-large {
    font-size: 4.125rem;
}

.fw-black {
    font-weight: 900 !important;
}

.team-detail {
    background-color: var(--background-color);
    color: var(--accent-color);
    transition: all 0.5s;
}

.team-detail:hover {
    background-color: var(--accent-color);
    color: var(--primary);
}

.divider {
    display: flex;
    align-items: center;
}

.divider::after {
    display: block;
    content: "";
    border-bottom: 0;
    flex-grow: 1;
    border-top: 3px solid #8692af;
    max-width: 30px;
    min-width: 30px;
}


.divider-element {
    letter-spacing: 2px;
    flex-shrink: 0;
    flex-grow: 1;
    margin: 0;
    margin-left: 1rem;
    font-weight: 400;
}

.image-infinite-bg {
    height: 90vh;
}

.animation-bg {
    animation: background_animation 10s forwards;
}

.bg-attach-fixed {
    background-attachment: fixed;
    background-position: center;
    width: 100%;
    height: 100%;
}

.bg-attach-cover {
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 100%;
}

.social-container {
    display: flex;
    flex-direction: row;
    gap: 1rem;
}

.social-container.column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.character-img {
    position: relative;
    z-index: 2;
}

.w-70 {
    width: 70%;
}

.stock-img {
    position: relative;
    z-index: 20;
}

.customer-item {
    border-radius: 50%;
    aspect-ratio: 1/1;
    font-size: 19px;
    width: 4rem;
    height: 4rem;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-2);
    margin-left: -6px;
    overflow: hidden;
}

.customer-item.single {
    margin-left: 0;
}

.testimonial-item {
    border-radius: 50%;
    aspect-ratio: 1/1;
    font-size: 19px;
    width: 5rem;
    height: 5rem;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    margin-left: -6px;
    overflow: hidden;
}

.bg-box {
    bottom: 0;
    right: 0;
    height: 60%;
    width: 50%;
    border: solid 3px var(--accent-color-2);
}

.icon-box {
    display: flex;
    justify-content: center;
    font-size: 30px;
    align-items: center;
    text-align: center;
    aspect-ratio: 1/1;
    transition: all 0.5s;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: var(--primary);
    padding: 10px;
    width: max-content;
    height: max-content;
}

.icon-box.hover {
    display: flex;
    justify-content: center;
    font-size: 15px;
    align-items: center;
    text-align: center;
    aspect-ratio: 1/1;
    transition: all 0.8s;
    border-radius: 50%;
    color: var(--primary);
    padding: 10px;
    width: max-content;
    height: max-content;
    border: 1px solid var(--accent-color);
    background-color: var(--accent-color);
}

.icon-box.hover:hover {
    background: transparent;
    color: var(--accent-color);
}

.icon-box-2 {
    display: flex;
    font-size: 60px;
    justify-content: center;
    align-items: center;
    text-align: center;
    aspect-ratio: 1/1;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
    height: max-content;
    width: max-content;
    padding: 10px;
    transition: all 0.5s;
}

.author-box {
    border-radius: 50%;
    aspect-ratio: 1/1;
    width: 15rem;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: -6px;
    overflow: hidden;
}

.post-button {
    background-color: transparent;
    color: var(--accent-color) !important;
    border: none !important;
}

.post-button:hover {
    background-color: transparent !important;
    color: var(--accent-color) !important;
    transform: scale(1.15);
}

.position-xl-absolute {
    position: absolute;
}

.w-60 {
    width: 60% !important;
}

.shadow-double {
    box-shadow: 40px -40px 0px -4px var(--accent-color), -54px 44px 0px -3px var(--text-color-2);
}

.shadow-single-left {
    box-shadow: -54px 44px 0px -3px var(--accent-color);
}

.shadow-single-right {
    box-shadow: 40px -40px 0px -4px var(--accent-color);
}

.shadow-accent-2 {
    -webkit-box-shadow: -90px -23px 0px 0px var(--accent-color);
    -moz-box-shadow: -90px -23px 0px 0px var(--accent-color);
    box-shadow: -90px -23px 0px 0px var(--accent-color);
}

.text-404 {
    font-size: 200px;
    font-weight: bold;
    font-family: var(--font-2);
}

.number-text {
    font-size: 96px;
    font-weight: bold;
}

.text-banner {
    font-size: 200px;
}

.rounded-end {
    border-top-right-radius: 1rem;
    border-bottom-right-radius: 1rem;
}

.swiperImage.floating-left {
    margin-left: 16rem;
}

.floating-heading {
    margin-left: -15rem;
}

.floating-image {
    position: absolute;
}

.floating-price {
    top: -2.5rem;
    right: -10rem;
}

.floating-banner {
    margin-top: -15rem;
}

.floating-top {
    margin-top: -10rem;
    margin-left: -5rem;
    margin-bottom: -5rem;
}

.floating-services {
    position: relative;
    z-index: 9999;
    margin-top: 3rem;
    margin-bottom: -3rem;
}

.floating-testi {
    margin-bottom: -8rem;
    margin-top: 19rem;
    margin-left: 15rem;
}

.floating-services-2 {
    margin-left: -5rem;
}

.floating-services-2 .padding {
    padding-left: 7rem;
}

.floating-services-3 {
    position: relative;
    margin-top: 3rem;
    margin-bottom: -3rem;
}

.floating-services-3 .padding {
    padding-left: 3rem;
    padding-right: 7rem;
}

.floating-bottom {
    position: absolute;
    top: 8rem;
    left: -5.5em;
}

.floating-bottom-1 {
    position: absolute;
    bottom: 5rem;
    right: 0;
}

.floating-bottom-2 {
    position: absolute;
    bottom: 5rem;
    right: 0;
}

.floating-contact {
    margin-left: -7rem;
    border-bottom-left-radius: 1rem;
}

.floating-counter {
    position: relative;
    margin-top: -6rem;
    z-index: 9999;
}

.floating-blog {
    margin-top: -3rem;
}

.image-container {
    position: relative;
    display: inline-block;
}

.hotspot {
    position: absolute;
    width: 30px;
    height: 30px;
    background: rgba(24, 21, 24, 0.5);
    border: 0.5px solid var(--text-color);
    backdrop-filter: blur(2px);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.hotspot:hover {
    background-color: var(--accent-color);
}

.hotspot i {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    transition: color 0.3s ease;
}

.hotspot:hover i {
    color: white;
}

/* Tooltip */
.hotspot::after {
    content: attr(data-text);
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    white-space: nowrap;
    display: none;
}

.hotspot:hover::after {
    display: block;
}

.d-inline-block {
    display: inline-block;
}

.position-responsive {
    position: absolute;
}

.list .icon-box {
    width: 4.3rem;
    height: 4.3rem;
}

.list-flush-horizontal {
    display: flex;
    flex-direction: row;
    list-style: none;
    margin: 0;
    padding: 1rem;
}

.list-flush-horizontal .list-item:first-child,
.list-flush-horizontal .list-item {
    border-right: 1px solid white;
}

.list-flush-horizontal .list-item:last-child {
    border-left: 1px solid white;
    border-right: none;
}

.list-flush-horizontal .list-item:nth-last-child(2) {
    border: none;
}

.list-group-item {
    background-color: transparent;
    border-radius: 10px;
}

.list-group .link {
    background-color: transparent;
    border: none;
    color: var(--text-color-2);
    transition: all 0.5s;
    border-radius: 0px;
    font-size: 16px;
    font-family: var(--font-1);

}

.list-group .link.active {
    color: var(--accent-color);

}

.list-group .link:hover {
    color: var(--accent-color) !important;
    text-decoration: none;
}

.list-group .list-group-item.active {
    background-color: var(--accent-color-2);
    color: var(--accent-color);
}

.list-group .list-group-item.list-group-item-action:hover {
    background-color: var(--accent-color-2);
    color: white;
}

.list {
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    padding-inline: 0.5rem;
}

.list .link {
    font-weight: 400;
    text-wrap: nowrap;
}

.list li {
    padding: 0;
    font-size: 16px;
    font-family: var(--font-2);
}

.list li .link {
    transition: all 0.5s;
    color: var(--text-color-2);
}

.list li i {
    transition: all 0.5s;
    color: var(--accent-color);
}

.list.text-black i {
    color: #131313;
}

.list li .link:hover,
.list li .link:hover i {
    color: var(--accent-color);
}

.countdown {
    display: flex;
    gap: 20px;
}

.countdown-box {
    background-color: var(--accent-color);
    color: white;
    text-align: center;
    padding: 30px;
    width: 200px;
    border-radius: 8px;
}

.countdown-box h2 {
    margin: 0;
    font-size: 2em;
}

.countdown-box p {
    margin: 0;
    font-size: 1.2em;
}

/* ---------------------------- */
/* Social and Contact Setting    */
/* ---------------------------- */
.customer-container {
    display: flex;
    flex-direction: row-reverse;
}

.customer-item:nth-child(1) {
    z-index: 6;
}

.customer-item:nth-child(2) {
    z-index: 5;
}

.customer-item:nth-child(3) {
    z-index: 4;
}

.customer-item:nth-child(4) {
    z-index: 3;
}

.subscribe-container {
    box-sizing: border-box;
    margin-bottom: -8em;
}

.contact-item {
    border-radius: 50%;
    aspect-ratio: 1/1;
    font-size: 40px;
    height: 2rem;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}


.social-item {
    border-radius: 50%;
    aspect-ratio: 1/1;
    font-size: 16px;
    width: 28px;
    height: 28px;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary);
    background-color: var(--text-color);
}

.social-item-2 {
    border-radius: 50%;
    aspect-ratio: 1/1;
    font-size: 24px;
    width: 20px;
    height: 20px;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-color-2);
}

.social-item-3 {
    border-radius: 50%;
    aspect-ratio: 1/1;
    font-size: 24px;
    width: 20px;
    height: 20px;
    transition: all 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--accent-color);
}

.social-container.accent .social-item {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.social-container.accent .social-item:hover {
    background-color: var(--accent-color);
    color: white;

}

.social-container.share .social-item {
    background-color: var(--accent-color);
    color: white;
}

.social-container.share .social-item:hover {
    background-color: var(--accent-color);
    color: white;
    border: 1px solid var(--accent-color);
}


.social-container.team .social-item {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 16px;
}


.social-item:hover {
    background-color: var(--accent-color);
}

.social-item-2:hover {
    color: var(--accent-color);
}

.social-item-3:hover {
    color: var(--text-color);
}

.social-container .share-button {
    background-color: var(--accent-color-1);
    aspect-ratio: 1/1;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.share-button:hover {
    background-color: var(--accent-color-2);
}

/* ---------------------------- */
/* Breadcrumb    */
/* ---------------------------- */
.breadcrumb {
    align-items: center;
    font-family: var(--font-2);
}

.breadcrumb .breadcrumb-item>a {
    color: var(--text-color);
}

.breadcrumb .breadcrumb-item.active {
    color: var(--accent-color);
    font-family: var(--font-2);
}

.breadcrumb-item+.breadcrumb-item::before {
    color: var(--accent-colorbolee);
}

/* ---------------------------- */
/* Specific Media Queries       */
/* ---------------------------- */
.video-e119 {
    width: 60%;
    margin-bottom: -3rem;
    margin-left: -3rem;
}

.ifr-video {
    aspect-ratio: 16/9;
    width: 100%;
}

.video-container {
    aspect-ratio: 3/2;
    background-size: cover;
    background-position: center;
    position: relative;
    border: 5px solid white;
    border-radius: 10px;
}

.video-iframe {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.video-btn {
    border-radius: 50%;
    aspect-ratio: 1/1;
    width: 4rem;
    background-color: var(--accent-color);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    font-size: 2rem;
    color: white;
    border: none;
    opacity: 0.7;
}

.video-btn:hover {
    opacity: 1;
    color: white;
}

.request-loader {
    position: relative;
    height: 60px;
    width: 60px;
    border-radius: 50% !important;
    background-color: var(--text-color);
    border: solid 2px var(--text-color);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary);
    font-size: 25px;
    aspect-ratio: 1/1;
}

.request-loader:hover {
    color: var(--text-color);
    background: transparent;
}

.request-loader::after,
.request-loader::before {
    opacity: 0.2;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    content: '';
    height: 100%;
    width: 100%;
    color: var(--text-color);
    border: 4px solid currentColor;
    border-radius: 50%;
    animation-name: ripple;
    animation-iteration-count: infinite;
    animation-timing-function: cubic-bezier(.65, 0, .34, 1);
    z-index: 0;
}

.request-loader::after {
    animation-delay: 0.5s;
    animation-duration: 3s;
}

.request-loader::before {
    animation-delay: 0.2s;
    animation-duration: 3s;
}


/* ---------------------------- */
/* card Setting       */
/* ---------------------------- */
.card {
    border: none;
    border-radius: 10px;
    transition: all 0.5s;
    background-color: var(--primary);
}

.card-accent {
    color: var(--accent-color);
    position: relative;
    background-size: cover;
    background-position: center;
    border-width: 1px;
    border-style: solid;
    border-radius: 10px;
    border-image: linear-gradient(to left, #A502A8, #2F4A9D)1;
}

.card-blog h4 {
    color: var(--text-color);
}

.card-blog:hover h4 {
    color: var(--accent-color);
}

.card-blog h6 {
    color: var(--text-color);
}

.card-blog:hover h6 {
    color: var(--accent-color);
}

.card-service {
    color: var(--text-color);
    border-radius: 0px;
    border: 1px solid transparent;
    padding: 30px 30px;
}

.card-service:hover {
    color: var(--text-color);
    border: 1px solid var(--accent-color);
    background-color: var(--accent-color-4);
}

.background-hover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

.card-service:hover .background-hover {
    opacity: 1;
    background: linear-gradient(118.48deg, rgba(47, 74, 157, 0.4) 0%, rgba(165, 2, 168, 0.4) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.9));
    border-radius: 10px;

}

.card-service.img-hover:hover {
    background-image: var(--url-image);
}

.image-hover {
    opacity: 0;
    position: absolute;
}

.card-service:hover .image-hover {
    opacity: 100%;
}


.card-testimonial {
    border: none;
    border-radius: 20px;
    transition: all 0.5s;
    background-color: var(--accent-color-3);

}

.card .icon-box.bg-accent-color {
    background-color: var(--accent-color);
    color: var(--accent-color);
}

.card .icon-box.accent-color-2 {
    color: var(--accent-color-2);
    font-size: 4rem;
}

.card:hover .icon-box.accent-color-2 {
    color: var(--accent-color-2);
}

.card:hover {
    transform: translateY(-5px);
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.card-testimonial:hover {
    transform: translateY(-20px);
    box-shadow: 0px 0px 0px 2px var(--accent-color);
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.card.card-pricing-hover {
    color: var(--text-color);
    border: 1px solid #01c7f349;
    border-radius: 20px;
    background: linear-gradient(140.72deg, rgba(30, 30, 30, 0.7) -67.01%, rgba(5, 5, 5, 0.7) 100.85%);
    backdrop-filter: blur(12.4px);
    transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease, color 0.3s ease;
}

.box-price {
    border: 1px solid;
    border-image: linear-gradient(210deg, #00000045, #01c7f349) 1;
    box-shadow:
        0 7px 15px 0 rgba(0, 0, 0, 0.13),
        0 1px 4px 0 rgba(0, 0, 0, 0.11);
    border-radius: 10px;
    background: linear-gradient(140.72deg, rgba(30, 30, 30, 0.7) -67.01%, rgba(5, 5, 5, 0.7) 100.85%);
    backdrop-filter: blur(12.4px);
}

.card.card-pricing-hover:hover h3 {
    color: var(--accent-color);
}

.card.card-pricing-hover:hover {
    border: 1px solid var(--accent-color-2);
    background: linear-gradient(180deg, #01C7F3 -81.79%, #111111 100%);

    transform: scale(1.05);
}

.card-pricing-hover-middle {
    transform: scale(1.04);
    flex: 1.00;
    box-shadow: 0px 0px 0px 2px var(--accent-color);
    color: white;
}


.card.card-pricing-hover:hover .btn-accent-outline {
    background-color: var(--text-color);
    color: var(--primary);
}

.card.card-pricing-hover .btn-accent-outline i {
    color: var(--text-color);
}

.card.card-pricing-hover:hover .btn-accent-outline i {
    color: var(--primary);
}

.card:hover .icon-box.bg-accent-color {
    background-color: var(--accent-color);
    color: var(--accent-color-2);

}

.card.card-outline-hover {
    box-shadow: 0 7px 15px 0 rgba(0, 0, 0, .13), 0 1px 4px 0 rgba(0, 0, 0, .11);
    border: 1px solid var(--accent-color);
}

.card.blog {
    background-color: var(--background-color);
}

.card.blog:hover {
    border: solid 1px var(--accent-color-2);
    border-radius: 10px;
}


.card.card-outline-hover:hover .btn-accent {
    background-color: transparent;
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
}

.card-overlay {
    background-color: transparent;
}

.card-overlay .card-body {
    position: absolute;
    width: 100%;
    bottom: 0;
    top: 0;
    left: 0;
    opacity: 0;
    transform: scaleY(0);
    transform-origin: bottom;
    transition: all 0.5s;
}

.card-overlay:hover {
    transform: scale(1.05);
}

.card-overlay .card-footer {
    color: var(--text-color);
}

.card-overlay:hover .card-footer {
    color: var(--accent-color);
}

.card-overlay .artist-text {
    transition: all 0.5s;
    transform: rotate(270deg);
    color: var(--text-color);
    position: absolute;
    bottom: 0;
    left: 15px;
    z-index: 2;
    transform-origin: left;
    line-height: 0;
}

.card-overlay img {
    padding-left: 40px;
    transition: all 0.5s;
}

.card-overlay.service img {
    padding-left: 0;
}

.card-overlay:hover img {
    padding-left: 0;
}

.card-overlay:hover .artist-text {
    padding-block: 2rem;
    transform: rotate(360deg);
    padding-inline: 0.5rem;
}

.card-overlay:hover .card-body {
    background: linear-gradient(180deg, rgba(207, 171, 130, 0) 62.08%, #CFAB82 100%);
    transform: scaleY(1);
    opacity: 1;
    padding: 0;
}

.card:hover .icon-box.bg-accent-color {
    background-color: white;
    color: var(--accent-color);
}

.card:hover p {
    transition: all 0.5s;
}

.card.with-border-bottom {
    border-bottom: 5px solid var(--accent-color) !important;
}

.card-about {
    background-color: var(--background-color);
    padding: 3rem;
    transition: all 0.8s;
}


.card-about:hover p {
    color: var(--primary);
}

.card-about:hover .icon-box-2 {
    background-color: var(--primary);
    color: var(--accent-color-2);
}

.card-about:hover {
    background-color: var(--accent-color-2);
    margin-top: -5rem;
    color: var(--primary);
    height: calc(100% + 5rem);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

.testimonial-container {
    background-color: var(--background-color);
    padding: 30px 30px;
    border-radius: 0;
    border: 1px solid transparent;
    color: var(--text-color-2);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    height: 440px;
}

.testimonial-container:hover {
    box-shadow: none;
    height: 440px;
    transition: all 0.5s;
    border: 1px solid rgba(207, 171, 130, 0.2);
    background-color: var(--accent-color-4);
    backdrop-filter: blur(12.4px);
}

.testimonial-container:hover .team-name {
    color: var(--accent-color);
}

.testimonial-container .icon-hover {
    color: var(--accent-color-3);
    font-size: 84px;
    transition: all 0.5s;
}

.testimonial-container:hover .icon-hover {
    position: absolute;
    top: 0;
    right: 0;
    color: var(--accent-color-2);
    font-size: 37px;
    margin-top: 1rem;
    margin-right: 2rem;
}

.services-container {
    background-color: transparent;
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    height: 100%;
    box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.1);
}

.features-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
}

.feature-box {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background-color: var(--accent-color);
    color: var(--primary);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    text-align: center;
    height: 100%;
    transition: transform 0.3s ease;
}

.feature-box-middle {
    transform: scaleY(1.15) scaleX(1.05);
}

.feature-box-middle h3,
.feature-box-middle p {
    position: relative;
    transform: none;
    will-change: contents;
}

/* Class Cards */
.class-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    flex-wrap: wrap;
}

.class-course {
    display: none;
}

.class-course.active {
    display: block;
}

.class-card:hover {
    transform: translateY(-10px);
    transition: all 0.5s;
}

.class-card.active {
    display: block;
    /* Show active cards */
}

.class-info {
    color: var(--primary);
    margin: 20px;
    font-family: var(--font-2);
    font-size: 12px;
    position: absolute;
    bottom: 0;
    left: 0;
}

.class-speakers {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 15px;
    margin-top: 10px;
}

.class-price {
    font-size: 1.2em;
    color: var(--primary);
    display: flex;
    flex-direction: column;
    align-self: flex-end;
    width: max-content;
}

.class-link {
    color: #ff3b3b;
    font-weight: bold;
    margin-top: 10px;
    text-align: left;
    display: inline-block;
}

/* Tab Navigation */
.tab-container.allteam {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin: 20px 0;
    font-family: var(--font-2);
}

.tab-container {
    width: 100%;
    position: relative;
    text-align: center;
}

.tab-container.team {
    display: flex;
    flex-direction: row;
    gap: 3rem;
    width: 100%;
    align-items: center;
}

.tab-container.faq {
    display: flex;
    flex-direction: row;
    gap: 3rem;
    width: 100%;
}


.background-container {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: -1;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 100%;
}

.tabs {
    display: flex;
    justify-content: space-around;

}

.tabs .card-overlay img {
    padding-left: 0;
    transition: all 0.5s;
}

.class-card .card-overlay img {
    padding-left: 0;
    transition: all 0.5s;
}

.tabs.studio {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    position: absolute;
    z-index: 2;
    background: rgba(35, 31, 32, 0.3);
    backdrop-filter: blur(17.5px);
    padding: 30px;

}

.tabs.faq {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 30px;
    background-color: var(--background-color);
    border: 1px solid var(--accent-color-3);
    height: max-content;
}

.tabs.team {
    display: flex;
    flex-direction: row;
    gap: 1rem;

}

.tabs.faq .tab {
    flex: 1;
    text-align: left;
    display: flex;
    flex-direction: row;
    padding: 16px 24px;
    gap: 1rem;
    justify-content: space-between;
    cursor: pointer;
    color: var(--text-color);
    transition: background 0.5s ease-in-out;
    border-right: none;
}

.tabs.team .tab {
    flex: 1;
    text-align: left;
    cursor: pointer;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color-2);
    transition: background 0.5s ease-in-out;
    border-right: none
}

.tabs.studio .tab {
    flex: 1;
    text-align: left;
    padding: 10px;
    cursor: pointer;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color-2);
    transition: background 0.5s ease-in-out;
    border-right: none
}

.tab {
    flex: 1;
    text-align: center;
    cursor: pointer;
    color: var(--text-color);
    transition: background 0.5s ease-in-out;
    border-right: 1px solid var(--text-color);
}

.tab-container.allteam .tab {
    flex: 1;
    text-align: center;
    cursor: pointer;
    color: var(--text-color);
    transition: background 0.5s ease-in-out;
    border-right: none;
    padding-block: 10px;
}

.tab-container.allteam .tab.active {
    color: var(--accent-color);
    border-bottom: 1px solid var(--accent-color);
}

.tab.active {
    color: var(--accent-color);
}

.tab .title-tab {
    font-size: 32px;
    font-weight: bold;
    padding: 30px;
}

.tab.active .title-tab {
    background-color: var(--primary);
}

.tabs.studio .tab.active {
    color: var(--primary);
    background: var(--accent-color)
}

.tabs.faq .tab.active {
    color: var(--primary);
    background: var(--accent-color)
}

.tab-content.studio {
    position: relative;
    bottom: unset;
    left: unset;
    max-width: unset;
    background: var(--primary);
    padding: 40px;
    text-align: left;
}

.tab-content.faq {
    position: relative;
    bottom: unset;
    left: unset;
    max-width: unset;
    background: var(--accent-color-3);
    padding: 40px;
    text-align: left;
}

.tab-content.team {
    position: relative;
    bottom: unset;
    left: unset;
    max-width: unset;
    background: var(--primary);
    text-align: left;
}

.content {
    display: none;
}

.content.active {
    display: block;
}

.tab .content {
    display: none;
    position: absolute;
    bottom: 10rem;
    margin-left: -4rem;
    max-width: 500px;
    padding: 40px;
    text-align: left;
    background: rgba(35, 31, 32, 0.3);
    backdrop-filter: blur(17.5px);
    /* Sembunyikan semua konten */
}

.tab.active .content {
    display: block;
    /* Tampilkan konten yang aktif */
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* 3 kolom */
    gap: 0;
    /* Jarak antar elemen 0 untuk mengatur garis */
    position: relative;
}

.grid-item-1 {
    border-right: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kanan */
    border-bottom: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis horizontal bawah */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    /* Ruang untuk gambar */
}

.grid-item-2 {
    border-right: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kanan */
    border-bottom: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis horizontal bawah */
    border-left: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kiri */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    /* Ruang untuk gambar */
}

.grid-item-3 {
    border-bottom: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis horizontal bawah */
    border-left: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kiri */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    /* Ruang untuk gambar */
}

.grid-item-4 {
    border-right: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kanan */
    border-top: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis horizontal atas */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    /* Ruang untuk gambar */
}

.grid-item-5 {
    border-right: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kanan */
    border-left: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis vertikal kiri */
    border-top: 1px solid rgb(255, 255, 255, 0.61);
    /* Garis horizontal atas */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    /* Ruang untuk gambar */
}

.grid-item-6 {
    border-left: 1px solid rgba(255, 255, 255, 0.61);
    /* Garis vertikal kiri */
    border-top: 1px solid rgba(255, 255, 255, 0.61);
    /* Garis horizontal atas */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px;
    /* Ruang untuk gambar */
}

/* ---------------------------- */
/* Progress and Rating       */
/* ---------------------------- */
.r-progress {
    --value: 17;
    --progress-color: var(--text-color);
    --secondary-progress-color: var(--accent-color-2);
    --animation-duration: 2000;
}

.r-progress-bar {
    position: relative;
    height: 8px;
    background-color: var(--secondary-progress-color);
    display: flex;
    border-radius: 3px;
    /* overflow: hidden; */
}

.r-progress-bar .progress-value {
    height: 100%;
    width: calc(var(--progress) * 1%);
    background-color: var(--progress-color);
    position: relative;
    border-radius: 3px;
    animation: load;
    animation-fill-mode: forwards;
    animation-duration: calc(var(--animation-duration) * 1ms);
    animation-timing-function: linear;
    animation-delay: 500ms;
    color: black;
}

.r-progress-bar.percentage-label::after {
    counter-reset: percentage var(--progress);
    content: counter(percentage) '%';
    display: block;
    position: absolute;
    left: calc((var(--progress) * 1%));
    animation: load;
    animation-fill-mode: forwards;
    animation-duration: calc(var(--animation-duration) * 1ms);
    animation-timing-function: linear;
    animation-delay: 500ms;
    font-size: 18px;
    line-height: 1.2;
    /* font-weight: 700; */
    font-family: var(--font-1);
    bottom: calc(100% + 0.5rem);
}

.rating {
    list-style: none;
    display: flex;
    flex-direction: row;
    gap: 0.75rem;
    padding: 0;
    margin: 0;
}

.rating li {
    color: #f1c644;
}

.rating li.inactive {
    color: #d9d9d9;
}

.glass-effect {
    background: var(--accent-color-3);
    opacity: 0.9;
    backdrop-filter: blur(11px);
    -webkit-backdrop-filter: blur(11px);
}

#player-container {
    width: 100%;
    height: 100%;
    margin-top: 5rem;
}

#player-bg-artwork {
    position: fixed;
    top: -30px;
    right: -30px;
    bottom: -30px;
    left: -30px;
    background-image: url("https://raw.githubusercontent.com/himalayasingh/music-player-1/master/img/_1.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 50%;
    filter: blur(40px);
    -webkit-filter: blur(40px);
    z-index: 1;
}

#player-bg-layer {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #fff;
    opacity: 0.5;
    z-index: 2;
}

#player {
    position: relative;
    height: 100%;
    z-index: 3;
}

#player-track {
    width: auto;
    flex: 1 1 0px;
    height: 100%;
    padding: 13px 10px 10px 13px;
    border-radius: 15px 15px 0 0;
    transition: 0.3s ease top;
    z-index: 1;
}

#album-name {
    color: var(--text-color);
    font-size: 17px;
    font-weight: bold;
}

#track-name {
    color: var(--accent-color);
    font-size: 13px;
    margin: 2px 0 13px 0;
}

#track-time {
    height: 12px;
    margin-bottom: 3px;
    overflow: hidden;
}

#current-time {
    float: left;
}

#track-length {
    float: right;
}

#current-time,
#track-length {
    color: var(--text-color);
    font-size: 11px;
    border-radius: 10px;
    transition: 0.3s ease all;
}

#track-time.active #current-time,
#track-time.active #track-length {
    color: var(--text-color);
    background-color: transparent;
}

#seek-bar-container,
#seek-bar {
    position: relative;
    height: 4px;
    border-radius: 4px;
}

#seek-bar-container {
    background-color: var(--text-color);
    cursor: pointer;
}

#seek-time {
    position: absolute;
    top: -29px;
    color: #fff;
    font-size: 12px;
    white-space: pre;
    padding: 5px 6px;
    border-radius: 4px;
    display: none;
}

#s-hover {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    opacity: 0.2;
    z-index: 2;
}

#seek-time,
#s-hover {
    background-color: #3b3d50;
}

#seek-bar {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 0;
    background-color: var(--accent-color);
    transition: 0.2s ease width;
    z-index: 1;
}

#player-content {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
    justify-content: flex-start;
    padding: 16px;
    width: 100%;
    background: rgba(24, 21, 24, 0.3);
    backdrop-filter: blur(17.5px);

}

#album-art {
    width: 115px;
    height: 115px;
    transform: rotateZ(0);
    transition: 0.3s ease all;
    border-radius: 50%;
    overflow: hidden;
}

#album-art.active {
    top: -60px;
    box-shadow: 0 0 0 4px #fff7f7, 0 30px 50px -15px #afb7c1;
}

#album-art:before {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    width: 20px;
    height: 20px;
    margin: -10px auto 0 auto;
    background-color: var(--accent-color-3);
    border-radius: 50%;
    box-shadow: inset 0 0 0 2px #fff;
    z-index: 2;
}

#album-art img {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: -1;
}

#album-art img.active {
    opacity: 1;
    z-index: 1;
}

#album-art.active img.active {
    z-index: 1;
    animation: rotateAlbumArt 3s linear 0s infinite forwards;
}

@keyframes rotateAlbumArt {
    0% {
        transform: rotateZ(0);
    }

    100% {
        transform: rotateZ(360deg);
    }
}

#buffer-box {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    height: 13px;
    color: #1f1f1f;
    font-size: 13px;
    text-align: center;
    font-weight: bold;
    line-height: 1;
    padding: 6px;
    margin: -12px auto 0 auto;
    opacity: 0;
    z-index: 2;
}

#album-art img,
#buffer-box {
    transition: 0.1s linear all;
}

#album-art.buffering img {
    opacity: 0.25;
}

#album-art.buffering img.active {
    opacity: 0.8;
    filter: blur(2px);
    -webkit-filter: blur(2px);
}

#album-art.buffering #buffer-box {
    opacity: 1;
}

#player-controls {
    width: auto;
    display: flex;
    gap: 2rem;
    height: 100%;
    float: right;
    overflow: hidden;
}

.control {
    width: 33.333%;
    float: left;
    padding: 12px 0;
}

.button i {
    display: block;
    color: #d6dee7;
    font-size: 16px;
    text-align: center;
    line-height: 1;
}

.button,
.button i {
    transition: 0.2s ease all;
}

.button:hover i {
    color: #fff;
}

/* ---------------------------- */
/* Accordion                    */
/* ---------------------------- */
.accordion {
    --bs-accordion-bg: transparent;
    --bs-accordion-btn-padding-x: 0;
}

.accordion .accordion-item {
    background-color: transparent;
    border: none;
    color: var(--text-color-2);
    outline: none;
    border-radius: 0;
    border-bottom: 1px solid var(--text-color-2);
}

.accordion-button:focus {
    box-shadow: none;
}

.accordion .accordion-button {
    background-color: transparent;
    color: var(--text-color-2);
    outline: none;
    border-radius: 0 !important;
    font-family: var(--font-1);
    font-size: 24px;
    font-weight: 600;
    /* box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px; */
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: start;
    padding-block: 1.2rem;
    color: var(--text-color);
}

.accordion .accordion-button.accent {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
}


.accordion-button::after {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%23CFAB82" class="bi bi-plus" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/></svg>');
}

.accordion-button:not(.collapsed)::after {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="%23CFAB82" class="bi bi-dash" viewBox="0 0 16 16"><path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8"/></svg>')
}

.accordion .accordion-button:not(.collapsed) {
    border: none;
    background-color: transparent;
    outline: none;
    box-shadow: none;
}

.accordion .accordion-body {
    background-color: transparent;
    font-size: 16px;
    color: var(--text-color-2);
    font-family: var(--font-2);
    padding-inline: 0;
}


@media only screen and (max-width:993px) {

    /* ---------------------------- */
    /* Typography                   */
    /* ---------------------------- */
    h1 {
        font-size: 30px;
    }

    h2 {
        font-size: 38px;
    }

    h3 {
        font-size: 28px;
    }

    h4 {
        font-size: 22px;
    }

    h5 {
        font-size: 18px;
    }

    h6 {
        font-size: 14px;
    }

    p,
    button,
    a {
        font-size: 13px;
    }

    .text-banner {
        font-size: 50px;
    }

    /* ---------------------------- */
    /* Button and Links Setting     */
    /* ---------------------------- */
    .btn {
        font-size: 13px;
        width: 100%;
    }

    /* ---------------------------- */
    /* Header and Navigation Setting  */
    /* ---------------------------- */
    .logo-container {
        max-width: 100px;
    }

    .nav-link {
        padding-block: 0.2rem;
        text-align: center;
    }

    #header {
        background: var(--background-color);
        backdrop-filter: blur(11px);
        -webkit-backdrop-filter: blur(11px);
    }

    /* ---------------------------- */
    /* Utility Classes              */
    /* ---------------------------- */
    .p-banner {
        color: var(--text-color);
    }

    .w-max-content {
        width: 100%;
    }

    .section {
        padding: 4em 2em 4em 2em;
    }

    .divider {
        width: 330px;
    }

    .fs-very-large {
        font-size: 3.125rem;
    }

    .text-404 {
        font-size: 8rem;
        font-weight: 700;
    }

    .image-absolute-1 {
        left: 45%;
        top: 35%;
    }

    .image-infinite-bg {
        background-size: cover !important;
    }


    .border-custom {
        border-width: 0px 0px 1px 0px;
    }

    .outer-margin {
        margin-right: 0;
    }

    .banner-image {
        margin: 0;
        transform: none;
    }

    .testimonial-img {
        margin: 0;
        margin-bottom: 1rem;
    }

    .dropdown-menu {
        width: 100%;
        box-shadow: none;
    }

    .video-e119 {
        width: 85%;
        margin-left: -1.5rem;
    }

    .dropdown-item {
        padding-block: 0.35rem;
    }

    .floating-image {
        position: inherit;
    }

    .floating-price {
        top: -2.5rem;
        right: -7.5rem;
    }

    .floating-heading {
        margin-left: 0;
    }

    .floating-banner {
        top: 0;
        left: 0;
        right: 0;
        margin-right: 1rem;
        margin-left: 1rem;
        margin-top: -10rem;
    }

    .floating-top {
        position: relative;
        margin-top: 0;
        margin-left: 0;
        margin-bottom: 0;
    }

    .floating-testi {
        margin-bottom: 1rem;
        margin-top: 1rem;
        margin-left: 1rem;
        margin-right: 1rem;
    }

    .floating-services {
        position: relative;
        z-index: 9999;
        margin-top: 3rem;
        margin-bottom: 1rem;
    }

    .floating-services-2 {
        margin-left: 0;
    }

    .floating-services-2 .padding {
        padding-left: 3rem;
    }

    .floating-services-3 {
        position: relative;
        margin-top: 1rem;
        margin-bottom: -3rem;
    }

    .floating-services-3 .padding {
        padding-left: 3rem;
        padding-right: 3rem;
    }

    .floating-bottom {
        position: initial;
        top: 0;
        left: 0;
    }

    .floating-bottom-1 {
        position: initial;
        bottom: 0;
        right: 0;
        left: 0;
    }

    .floating-bottom-2 {
        position: initial;
        bottom: 0;
        right: 0;
        left: 0;
        margin-top: 1rem;
    }

    .floating-contact {
        margin-left: 0rem;
    }

    .floating-counter {
        position: relative;
        margin-top: -5rem;
        z-index: 9999;
    }

    .floating-blog {
        margin-top: 0;
    }

    .border-testimonial {
        border-right: none;
    }

    .service-container {
        background-color: transparent;
        padding: 30px;
        display: flex;
        flex-direction: column;
        gap: 1.75rem;
        box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.1);
        height: 100%;
    }

    .appointment-box {
        top: -2rem;
        bottom: 0;
        left: 0;
        right: 0;
        height: 8rem;
    }

    .w-md-70 {
        width: 70%;
    }

    .w-md-60 {
        width: 60%;
    }

    .position-responsive {
        position: relative;
    }

    .form-appointment-container {
        position: relative;
        transform: translateY(0);
    }

    .list-flush-horizontal {
        flex-direction: column;
    }

    .list-flush-horizontal .list-item:first-child,
    .list-flush-horizontal .list-item {
        border-right: none;
        border-bottom: 1px solid white;
    }

    .list-flush-horizontal .list-item:last-child {
        border-left: none;
        border-bottom: none;
        border-top: 1px solid white;
    }

    .position-xl-absolute {
        position: static;
    }

    .banner-heading {
        font-size: 2.5rem;
    }

    .tabs.studio {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        position: relative;
        z-index: 2;
        background: rgba(35, 31, 32, 0.3);
        backdrop-filter: blur(17.5px);
        padding: 30px;
        align-items: center;

    }

    .tabs {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
    }

    .tab-content {
        position: relative;
        left: 0;
    }

    .tab-container {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        justify-content: center;
        align-items: center;
    }

    .class-container {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        justify-content: center;
        align-items: center;
    }

    footer .d-flex.flex-column {
        text-align: center;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 0 0 0;
    }

    footer .link.d-flex.flex-row {
        text-align: center;
        justify-content: center;
        align-items: center;

    }

    footer .list {
        padding: 0 0 0 0;
    }

    .footer {
        position: relative;
    }

    .footer-img {
        position: relative;
    }

    .features-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
    }

    .testimonial-container {
        background-color: transparent;
        padding: 1rem;
        border-radius: 10px;

    }

    .swiperImage {
        padding-bottom: 0;
    }

    .service-scroll {
        padding-right: 1rem;
    }

    .swiper-button-next,
    .swiper-button-prev {
        top: 15rem;
        height: 80px;
        width: 80px;
        border: 5px solid var(--primary);
    }

    .swiper-button-next::after,
    .swiper-button-prev::after {
        font-size: 24px;
    }

    .mySwiper .swiper-button-next,
    .mySwiper .swiper-button-prev {
        top: 28rem;
        width: 40px;
        height: 40px;
    }

    /* Center the buttons horizontally */
    .mySwiper .swiper-button-next {
        right: 0;
        transform: translateY(20px);
    }

    .mySwiper .swiper-button-prev {
        left: 70%;
        transform: translateY(20px);
    }

    .swiperStep2 .swiper-button-next::after,
    .swiperStep2 .swiper-button-prev::after {
        font-size: 16px;
    }

    .swiperStep2 .swiper-button-next,
    .swiperStep2 .swiper-button-prev {
        width: 50px;
        height: 50px;
        border: 3px solid var(--primary);
    }

    /* Center the buttons horizontally */
    .swiperStep2 .swiper-button-next {
        right: 7%;
        transform: translateY(20px);
    }

    .swiperStep2 .swiper-button-prev {
        left: 7%;
        transform: translateY(20px);
    }

    .icon-box.link {
        font-size: 25px;
        padding: 25px;
    }

    .tab .content {
        margin-left: 0;
        position: static;
    }

}

/* ---------------------------- */
/* Audio Excellence Enhancements */
/* ---------------------------- */

/* Luxury cursor effects */
.lux-cursor {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="%23FF4A00"><circle cx="12" cy="12" r="3"/></svg>'), auto;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Enhanced hover effects for cards */
.lux-card-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.lux-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 74, 0, 0.1), transparent);
    transition: left 0.6s;
    z-index: 1;
}

.lux-card-enhanced:hover::before {
    left: 100%;
}

/* Glowing text effect */
.lux-glow-text {
    text-shadow: 0 0 10px var(--accent-color), 0 0 20px var(--accent-color), 0 0 30px var(--accent-color);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
    from { text-shadow: 0 0 10px var(--accent-color), 0 0 20px var(--accent-color), 0 0 30px var(--accent-color); }
    to { text-shadow: 0 0 5px var(--accent-color), 0 0 10px var(--accent-color), 0 0 15px var(--accent-color); }
}

/* Enhanced button effects */
.lux-btn-enhanced {
    position: relative;
    overflow: hidden;
    background: linear-gradient(45deg, var(--accent-color), var(--accent-color-2));
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 74, 0, 0.3);
}

.lux-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.lux-btn-enhanced:hover::before {
    left: 100%;
}

.lux-btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 74, 0, 0.4);
}

/* Parallax effect for hero sections */
.lux-parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Audio visualizer animation */
.lux-audio-bars {
    display: flex;
    align-items: end;
    gap: 3px;
    height: 40px;
    justify-content: center;
}

.lux-audio-bar {
    width: 4px;
    background: var(--accent-color);
    border-radius: 2px;
    animation: audio-wave 1.5s ease-in-out infinite;
}

.lux-audio-bar:nth-child(2) { animation-delay: 0.1s; }
.lux-audio-bar:nth-child(3) { animation-delay: 0.2s; }
.lux-audio-bar:nth-child(4) { animation-delay: 0.3s; }
.lux-audio-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes audio-wave {
    0%, 100% { height: 10px; }
    50% { height: 40px; }
}

/* Enhanced brand logo effects */
.lux-brand-logo {
    filter: grayscale(100%) brightness(0.8);
    transition: all 0.4s ease;
    transform: scale(1);
}

.lux-brand-logo:hover {
    filter: none;
    transform: scale(1.1);
    box-shadow: 0 5px 20px rgba(255, 74, 0, 0.3);
}

/* Floating elements */
.lux-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}